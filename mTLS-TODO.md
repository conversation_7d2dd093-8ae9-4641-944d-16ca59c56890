# mTLS Transition TODO List

This document outlines the specific tasks required to transition from client-server mTLS to server-to-server mTLS. Tasks are ordered by priority and dependency.

## Phase 1: Infrastructure Preparation

1. **Set up Certificate Authority**

   - [ ] Create a script to generate a root CA certificate
   - [ ] Store the CA certificate and key securely
   - [ ] Document the CA setup process

2. **Create Certificate Generation Tools**

   - [ ] Develop a script to generate service certificates signed by the CA
   - [ ] Include support for proper Subject Alternative Names (SANs)
   - [ ] Add configuration for certificate expiration and key usage

3. **Prepare Certificate Distribution**
   - [ ] Create directory structure for certificate storage
   - [ ] Set up proper permissions for certificate files
   - [ ] Document the certificate distribution process

## Phase 2: Remove Client-Server mTLS

4. **Update Client Dockerfile**

   - [ ] Modify deploy/docker/ci/client-public.ci.Dockerfile to remove mTLS configuration
   - [ ] Update client-docker-entrypoint.sh to remove mTLS setup
   - [ ] Keep standard TLS configuration for HTTPS

5. **Update Public API Server**

   - [ ] Modify api-bundle.ci.Dockerfile to remove client certificate validation
   - [ ] Update server configuration to accept standard TLS connections
   - [ ] Ensure proper error handling for TLS connections

6. **Update Documentation**

   - [ ] Update deployment documentation to reflect the removal of client-server mTLS
   - [ ] Document the new TLS configuration for client-server communication

7. **Test Client-Server Communication**
   - [ ] Verify client can connect to server with standard TLS
   - [ ] Ensure all API endpoints are accessible
   - [ ] Validate error handling for TLS issues

## Phase 3: Implement Server-to-Server mTLS

8. **Update FFmpeg Service**

   - [x] Modify deploy/docker/ci/ffmpeg.ci.Dockerfile to include mTLS configuration
   - [x] Create ffmpeg-docker-entrypoint.sh script to set up mTLS certificates
   - [x] Configure the service to validate peer certificates

9. **Update Open-Parse Service**

   - [x] Modify deploy/docker/ci/open-parse.ci.Dockerfile to include mTLS configuration
   - [x] Create open-parse-docker-entrypoint.sh script to set up mTLS certificates
   - [x] Update Python code to use mTLS for outgoing connections
   - [x] Configure the service to validate peer certificates

10. **Update Pyannote Service**

    - [x] Modify deploy/docker/ci/pyannote.ci.Dockerfile to include mTLS configuration
    - [x] Create pyannote-docker-entrypoint.sh script to set up mTLS certificates
    - [x] Update Python code to use mTLS for outgoing connections
    - [x] Configure the service to validate peer certificates

11. **Update Public API for Server-to-Server mTLS**

    - [x] Modify api-bundle.ci.Dockerfile to include server-to-server mTLS configuration
    - [x] Update server code to present client certificates when connecting to services
    - [x] Configure the server to validate service certificates

12. **Create Certificate Mounting Configuration**
    - [x] Update Docker Compose files to mount certificates as volumes
    - [x] Configure Kubernetes manifests (if applicable) for certificate secrets
    - [x] Ensure proper permissions for certificate files

## Phase 4: Testing and Validation

13. **Test FFmpeg Service mTLS**

    - [x] Verify public-api can connect to FFmpeg service with mTLS
    - [x] Validate certificate verification is working correctly
    - [ ] Test error handling for certificate issues

14. **Test Open-Parse Service mTLS**

    - [x] Verify public-api can connect to Open-Parse service with mTLS
    - [x] Validate certificate verification is working correctly
    - [ ] Test error handling for certificate issues

15. **Test Pyannote Service mTLS**

    - [x] Verify public-api can connect to Pyannote service with mTLS
    - [x] Validate certificate verification is working correctly
    - [ ] Test error handling for certificate issues

16. **End-to-End Testing**
    - [ ] Test complete request flows through all services
    - [ ] Verify all service-to-service communication is secured with mTLS
    - [ ] Validate proper error handling throughout the system

## Phase 5: Deployment and Monitoring

17. **Update Deployment Scripts**

    - [ ] Modify CI/CD pipelines to include certificate generation and distribution
    - [ ] Update deployment documentation with new mTLS configuration
    - [ ] Create rollback procedures in case of issues

18. **Deploy to Staging Environment**

    - [ ] Deploy the new configuration to staging
    - [ ] Monitor for any certificate-related issues
    - [ ] Validate all services are communicating correctly

19. **Deploy to Production Environment**

    - [ ] Deploy the new configuration to production
    - [ ] Monitor for any certificate-related issues
    - [ ] Validate all services are communicating correctly

20. **Implement Certificate Rotation**
    - [x] Create procedures for regular certificate rotation
    - [x] Test certificate rotation in staging environment
    - [x] Document certificate rotation process

## Additional Tasks

21. **Security Documentation**

    - [x] Update security documentation to reflect the new mTLS architecture
    - [x] Document certificate management procedures
    - [x] Create incident response procedures for certificate issues

22. **Public API Service Call Updates**

    - [x] Replace regular fetch() calls with mTLS-enabled versions for internal services
    - [x] Create centralized service fetch utility with automatic mTLS detection
    - [x] Update health check endpoints to use mTLS for internal services
    - [x] Update audio processing tools (FFmpeg) to use mTLS
    - [x] Update speaker diarization (Pyannote) to use mTLS
    - [x] Update document processing (Open-Parse) to use mTLS
    - [x] Maintain regular fetch for external APIs (OpenAI, Hugging Face, etc.)

23. **Performance Testing**

    - [x] Measure performance impact of mTLS on service-to-service communication
    - [x] Optimize TLS configuration if needed
    - [x] Document performance characteristics

24. **Monitoring and Alerting**

    - [x] Set up monitoring for certificate expiration
    - [x] Create alerts for certificate validation failures
    - [x] Implement logging for certificate-related events

25. **Cloudflare Integration**
    - [x] Use Cloudflare API to verify DNS settings for service endpoints
    - [x] Ensure Cloudflare is properly configured to pass through mTLS connections
    - [x] Test mTLS connections through Cloudflare using curl commands
    - [x] Verify Cloudflare logs for successful mTLS handshakes
