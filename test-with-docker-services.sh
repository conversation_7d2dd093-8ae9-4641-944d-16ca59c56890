#!/bin/bash
# Test inter-service communication with Docker services

set -e

# Configuration
ENVIRONMENT=${1:-staging}
COMPOSE_FILE="docker/${ENVIRONMENT}-mtls.yml"
TEST_DURATION=${2:-60}

echo "🔒 Testing Inter-Service Communication with Docker"
echo "Environment: $ENVIRONMENT"
echo "Compose File: $COMPOSE_FILE"
echo "Test Duration: ${TEST_DURATION}s"
echo ""

# Colors
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# Function to log with timestamp
log() {
  echo -e "[$(date '+%Y-%m-%d %H:%M:%S')] $1"
}

# Check if Docker Compose file exists
if [ ! -f "$COMPOSE_FILE" ]; then
  log "${RED}❌ Docker Compose file not found: $COMPOSE_FILE${NC}"
  exit 1
fi

# Function to wait for service to be ready
wait_for_service() {
  local service_name=$1
  local max_attempts=30
  local attempt=1
  
  log "${BLUE}⏳ Waiting for $service_name to be ready...${NC}"
  
  while [ $attempt -le $max_attempts ]; do
    if docker compose -f "$COMPOSE_FILE" ps "$service_name" | grep -q "Up"; then
      log "${GREEN}✅ $service_name is ready${NC}"
      return 0
    fi
    
    echo -n "."
    sleep 2
    attempt=$((attempt + 1))
  done
  
  log "${RED}❌ $service_name failed to start within $((max_attempts * 2)) seconds${NC}"
  return 1
}

# Function to test service endpoint from within a container
test_service_endpoint() {
  local from_service=$1
  local to_service=$2
  local endpoint=$3
  local method=${4:-GET}
  local description=$5
  
  log "${BLUE}🧪 Testing: $from_service -> $to_service$endpoint ($method)${NC}"
  log "   Description: $description"
  
  # Get container ID
  local container_id=$(docker compose -f "$COMPOSE_FILE" ps -q "$from_service")
  
  if [ -z "$container_id" ]; then
    log "${RED}❌ Container not found for $from_service${NC}"
    return 1
  fi
  
  # Test with mTLS (HTTPS)
  local https_url="https://$to_service:5000$endpoint"
  log "   🔒 Testing with mTLS: $https_url"
  
  if docker exec "$container_id" curl -k -s --max-time 10 "$https_url" > /dev/null 2>&1; then
    log "${GREEN}   ✅ mTLS connection successful${NC}"
    
    # Get response details
    local response=$(docker exec "$container_id" curl -k -s --max-time 10 "$https_url" 2>/dev/null || echo "")
    if [ -n "$response" ]; then
      log "   📄 Response: $(echo "$response" | head -c 100)..."
    fi
    
    return 0
  else
    log "${RED}   ❌ mTLS connection failed${NC}"
    
    # Test without mTLS (HTTP) for comparison
    local http_url="http://$to_service:5000$endpoint"
    log "   🌐 Testing without mTLS: $http_url"
    
    if docker exec "$container_id" curl -s --max-time 10 "$http_url" > /dev/null 2>&1; then
      log "${YELLOW}   ⚠️  HTTP connection works (mTLS may not be properly configured)${NC}"
    else
      log "${RED}   ❌ HTTP connection also failed${NC}"
    fi
    
    return 1
  fi
}

# Function to test service logs for mTLS activity
check_mtls_logs() {
  local service=$1
  local duration=${2:-30}
  
  log "${BLUE}📋 Checking $service logs for mTLS activity (${duration}s)...${NC}"
  
  # Capture logs for the specified duration
  timeout "$duration" docker compose -f "$COMPOSE_FILE" logs -f "$service" 2>/dev/null | \
    grep -i -E "(mtls|tls|certificate|handshake|ssl)" | \
    head -10 | \
    while read -r line; do
      if echo "$line" | grep -qi "success\|valid\|ok"; then
        log "${GREEN}🔐 mTLS Success: $line${NC}"
      elif echo "$line" | grep -qi "fail\|error\|invalid"; then
        log "${RED}❌ mTLS Error: $line${NC}"
      else
        log "${YELLOW}🔍 mTLS Activity: $line${NC}"
      fi
    done
}

# Function to run comprehensive service tests
run_service_tests() {
  log "${BLUE}🧪 Running Comprehensive Service Tests${NC}"
  echo ""
  
  local test_results=()
  
  # Test 1: Public API -> FFmpeg
  log "${BLUE}📋 Test 1: Public API -> FFmpeg Service${NC}"
  if test_service_endpoint "public-api" "audio-splitter-ffmpeg" "/health" "GET" "Health check"; then
    test_results+=("PASS: Public API -> FFmpeg health")
  else
    test_results+=("FAIL: Public API -> FFmpeg health")
  fi
  echo ""
  
  # Test 2: Public API -> Pyannote
  log "${BLUE}📋 Test 2: Public API -> Pyannote Service${NC}"
  if test_service_endpoint "public-api" "audio-speak-dia-pyannote" "/health" "GET" "Health check"; then
    test_results+=("PASS: Public API -> Pyannote health")
  else
    test_results+=("FAIL: Public API -> Pyannote health")
  fi
  echo ""
  
  # Test 3: Public API -> Open-Parse
  log "${BLUE}📋 Test 3: Public API -> Open-Parse Service${NC}"
  if test_service_endpoint "public-api" "open-parse" "/health" "GET" "Health check"; then
    test_results+=("PASS: Public API -> Open-Parse health")
  else
    test_results+=("FAIL: Public API -> Open-Parse health")
  fi
  echo ""
  
  # Test 4: Check system health endpoint
  log "${BLUE}📋 Test 4: System Health Endpoint${NC}"
  local api_container=$(docker compose -f "$COMPOSE_FILE" ps -q "public-api")
  if [ -n "$api_container" ]; then
    if docker exec "$api_container" curl -s --max-time 10 "http://localhost:8080/system/health" > /dev/null 2>&1; then
      log "${GREEN}✅ System health endpoint accessible${NC}"
      test_results+=("PASS: System health endpoint")
      
      # Get the actual response
      local health_response=$(docker exec "$api_container" curl -s --max-time 10 "http://localhost:8080/system/health" 2>/dev/null || echo "")
      if [ -n "$health_response" ]; then
        log "📄 Health Response: $(echo "$health_response" | head -c 200)..."
      fi
    else
      log "${RED}❌ System health endpoint failed${NC}"
      test_results+=("FAIL: System health endpoint")
    fi
  else
    log "${RED}❌ Public API container not found${NC}"
    test_results+=("FAIL: Public API container not found")
  fi
  echo ""
  
  # Print test results summary
  log "${BLUE}📊 Test Results Summary${NC}"
  local passed=0
  local total=${#test_results[@]}
  
  for result in "${test_results[@]}"; do
    if [[ $result == PASS:* ]]; then
      log "${GREEN}✅ $result${NC}"
      passed=$((passed + 1))
    else
      log "${RED}❌ $result${NC}"
    fi
  done
  
  echo ""
  log "📈 Overall Results: $passed/$total tests passed ($(( passed * 100 / total ))%)"
  
  return $((total - passed))
}

# Main execution
main() {
  log "${GREEN}🚀 Starting Docker-based Inter-Service Communication Tests${NC}"
  echo ""
  
  # Check if services are running
  log "${BLUE}🔍 Checking service status...${NC}"
  
  local services=("public-api" "audio-splitter-ffmpeg" "audio-speak-dia-pyannote" "open-parse")
  local all_running=true
  
  for service in "${services[@]}"; do
    if docker compose -f "$COMPOSE_FILE" ps "$service" | grep -q "Up"; then
      log "${GREEN}✅ $service is running${NC}"
    else
      log "${RED}❌ $service is not running${NC}"
      all_running=false
    fi
  done
  
  if [ "$all_running" = false ]; then
    log "${YELLOW}⚠️  Not all services are running. Starting services...${NC}"
    
    # Start services
    docker compose -f "$COMPOSE_FILE" up -d
    
    # Wait for services to be ready
    for service in "${services[@]}"; do
      wait_for_service "$service"
    done
    
    # Give services extra time to fully initialize
    log "${BLUE}⏳ Waiting for services to fully initialize...${NC}"
    sleep 30
  fi
  
  echo ""
  
  # Run service tests
  run_service_tests
  local test_exit_code=$?
  
  # Check logs for mTLS activity
  log "${BLUE}📋 Checking logs for mTLS activity...${NC}"
  for service in "${services[@]}"; do
    check_mtls_logs "$service" 10
  done
  
  echo ""
  
  # Final status
  if [ $test_exit_code -eq 0 ]; then
    log "${GREEN}🎉 All inter-service communication tests passed!${NC}"
    log "${GREEN}✅ mTLS is working correctly between services${NC}"
  else
    log "${YELLOW}⚠️  Some tests failed. This may indicate mTLS configuration issues.${NC}"
    log "${BLUE}💡 Check the logs above for specific error details.${NC}"
  fi
  
  echo ""
  log "${BLUE}📋 Next Steps:${NC}"
  log "   1. Review test results above"
  log "   2. Check service logs: docker compose -f $COMPOSE_FILE logs [service-name]"
  log "   3. Monitor ongoing mTLS activity: ./deploy/scripts/monitor-mtls.sh $ENVIRONMENT"
  log "   4. Stop services: docker compose -f $COMPOSE_FILE down"
  
  return $test_exit_code
}

# Run main function
main "$@"
