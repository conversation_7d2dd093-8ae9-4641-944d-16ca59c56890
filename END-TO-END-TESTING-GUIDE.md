# 🔄 End-to-End Testing Guide

## 🎯 **Overview**

This guide covers comprehensive end-to-end testing for the mTLS-enabled service architecture. The testing framework validates complete workflows through all services to ensure mTLS is working correctly in production scenarios.

## 🧪 **Testing Framework Components**

### **1. ✅ Workflow Testing Script**

- **File**: `test-end-to-end-workflows.js`
- **Purpose**: Test complete workflows without requiring Docker
- **Features**: mTLS certificate loading, HTTP/HTTPS testing, comprehensive reporting

### **2. ✅ Docker Integration Testing**

- **File**: `test-e2e-with-docker.sh`
- **Purpose**: Test with running Docker services
- **Features**: Service startup, mTLS monitoring, service-to-service communication

## 📋 **Test Workflows Covered**

### **Workflow 1: System Health Check**

Tests the basic health endpoints that verify system status.

**Steps:**

1. **Public API Health** - `GET /health`

   - Validates basic API availability
   - Checks mTLS certificate loading

2. **System Health with Service Checks** - `GET /system/health`
   - Validates internal service connectivity
   - Tests mTLS communication to worker services

**Expected Results:**

- ✅ HTTP 200 responses
- ✅ JSON responses with service status
- ✅ mTLS handshakes successful

### **Workflow 2: Audio Processing Pipeline**

Tests the complete audio processing workflow through FFmpeg and Pyannote services.

**Steps:**

1. **Check Audio Tools** - `GET /white-label/.../tools`

   - Lists available audio processing tools
   - Validates service discovery

2. **Check Service Health** - `GET /white-label/.../service-health`

   - Tests health of audio processing services
   - Validates mTLS connectivity

3. **Test Audio Processing (Dry Run)** - `POST /white-label/.../dryrun/diarize-video`
   - Tests audio processing workflow
   - Validates end-to-end pipeline

**Expected Results:**

- ✅ Service discovery working
- ✅ mTLS communication to FFmpeg service
- ✅ mTLS communication to Pyannote service
- ✅ Audio processing pipeline functional

### **Workflow 3: Document Processing Pipeline**

Tests document processing through Open-Parse service.

**Steps:**

1. **Check RAG Vector Tools** - `GET /white-label/.../rag-vector/files`
   - Tests document processing endpoints
   - Validates mTLS to Open-Parse service

**Expected Results:**

- ✅ Document processing endpoints available
- ✅ mTLS communication to Open-Parse service

### **Workflow 4: Internal Service Communication**

Tests direct service-to-service communication with mTLS.

**Steps:**

1. **FFmpeg Service Health** - `GET https://audio-splitter-ffmpeg:5000/health`
2. **Pyannote Service Health** - `GET https://audio-speak-dia-pyannote:5000/health`
3. **Open-Parse Service Health** - `GET https://open-parse:5000/health`

**Expected Results:**

- ✅ Direct mTLS connections successful
- ✅ Service certificates validated
- ✅ Mutual authentication working

### **Workflow 5: AI Chat Pipeline**

Tests AI chat functionality that may use multiple services.

**Steps:**

1. **AI Chat Health Check** - `GET /ai-chat/health`

**Expected Results:**

- ✅ AI chat endpoints available
- ✅ Multi-service workflows functional

## 🚀 **Running the Tests**

### **Option 1: Standalone Testing (No Docker Required)**

```bash
# Test without running services (validates framework)
node test-end-to-end-workflows.js staging
node test-end-to-end-workflows.js production
```

**Use Case:**

- Validate testing framework
- Check certificate loading
- Test endpoint definitions

### **Option 2: Docker Integration Testing**

```bash
# Test with running Docker services
./test-e2e-with-docker.sh staging
./test-e2e-with-docker.sh production
```

**Use Case:**

- Full end-to-end validation
- Real mTLS communication testing
- Production readiness verification

### **Option 3: Manual Service Startup + Testing**

```bash
# Start services
docker compose -f docker/staging-mtls.yml up -d

# Wait for services to be ready
sleep 30

# Run tests
node test-end-to-end-workflows.js staging

# Stop services
docker compose -f docker/staging-mtls.yml down
```

## 📊 **Test Results Interpretation**

### **✅ Success Indicators**

- **100% Success Rate**: All workflows passing
- **mTLS Handshakes**: Successful certificate authentication
- **Service Communication**: All internal services reachable
- **Response Validation**: Correct JSON responses from all endpoints

### **⚠️ Partial Success (Expected in Some Cases)**

- **75-99% Success Rate**: Most workflows passing
- **External Service Failures**: May fail due to test credentials
- **Missing Test Data**: Some endpoints may return 404 for test data

### **❌ Failure Indicators**

- **0-74% Success Rate**: Major configuration issues
- **Connection Refused**: Services not running
- **Certificate Errors**: mTLS configuration problems
- **DNS Resolution Failures**: Network configuration issues

## 🔍 **Troubleshooting Guide**

### **Connection Refused Errors**

```
❌ Error: connect ECONNREFUSED 127.0.0.1:9443
```

**Solutions:**

1. Start services: `docker compose -f docker/staging-mtls.yml up -d`
2. Check service status: `docker compose -f docker/staging-mtls.yml ps`
3. Verify port mappings in Docker Compose file

### **DNS Resolution Failures**

```
❌ Error: getaddrinfo ENOTFOUND audio-splitter-ffmpeg
```

**Solutions:**

1. Run tests from within Docker network
2. Use Docker service names for internal communication
3. Check Docker network configuration

### **mTLS Certificate Errors**

```
❌ Error: certificate verify failed
```

**Solutions:**

1. Verify certificates exist: `ls -la private-keys/staging/certs/mtls/`
2. Check certificate validity: `openssl x509 -in ca.crt -text -noout`
3. Regenerate certificates: `./deploy/scripts/generate-mtls-certs.sh staging`

### **Service Health Failures**

```
❌ System Health: 0/2 (0%)
```

**Solutions:**

1. Check service logs: `docker compose -f docker/staging-mtls.yml logs [service]`
2. Verify mTLS configuration in services
3. Check certificate mounting in Docker volumes

## 📈 **Performance Benchmarks**

### **Expected Response Times**

- **Health Endpoints**: < 100ms
- **Service Discovery**: < 200ms
- **mTLS Handshakes**: < 500ms
- **Complete Workflows**: < 2000ms

### **Success Rate Targets**

- **Development**: 75%+ (some external services may fail)
- **Staging**: 90%+ (production-like environment)
- **Production**: 95%+ (all services should be available)

## 🎯 **Test Coverage**

### **✅ What We Test**

- **mTLS Certificate Loading**: All certificate types
- **Service Discovery**: Internal service detection
- **HTTP/HTTPS Communication**: Both protocols
- **Service-to-Service**: Direct mTLS communication
- **Complete Workflows**: End-to-end functionality
- **Error Handling**: Connection failures and timeouts

### **✅ Security Validation**

- **Certificate Authentication**: Mutual TLS verification
- **Encrypted Communication**: All internal traffic secured
- **Service Isolation**: Proper network segmentation
- **Access Control**: Certificate-based authorization

## 🔄 **Continuous Integration**

### **CI/CD Integration**

```bash
# Add to CI pipeline
./deploy/scripts/deploy-production-mtls.sh staging
./test-e2e-with-docker.sh staging
```

### **Automated Testing Schedule**

- **Pre-deployment**: Run full test suite
- **Post-deployment**: Validate production health
- **Nightly**: Comprehensive workflow testing
- **On-demand**: Manual testing for debugging

## 🎉 **Success Criteria**

### **Production Readiness Checklist**

- [ ] All 5 workflows pass with 90%+ success rate
- [ ] mTLS handshakes successful for all internal services
- [ ] Certificate loading working in all environments
- [ ] Service-to-service communication functional
- [ ] No certificate validation errors
- [ ] Response times within acceptable limits

### **Deployment Approval**

- ✅ **Staging Tests**: 90%+ success rate
- ✅ **mTLS Validation**: All certificates working
- ✅ **Performance**: Response times acceptable
- ✅ **Security**: No certificate errors
- ✅ **Monitoring**: Logs show successful handshakes

**When all criteria are met, the mTLS implementation is ready for production deployment!** 🎉
