# mTLS configuration for staging environment
# Generated by deploy-mtls-certificates.sh

version: "3.8"

services:
  public-api:
    environment:
      - ENABLE_MTLS=true
      - DEBUG_MTLS=true
      - MTLS_CERT_DIR=/etc/ssl
      - MTLS_VERIFY_SERVER=true
    volumes:
      - ../deploy/certificates/staging/ca/ca.crt:/etc/ssl/ca/ca.crt:ro
      - ../deploy/certificates/staging/services/public-api/client.crt:/etc/ssl/certs/client.crt:ro
      - ../deploy/certificates/staging/services/public-api/client.key:/etc/ssl/private/client.key:ro

  audio-splitter-ffmpeg:
    environment:
      - ENABLE_MTLS=true
      - MTLS_CERT_DIR=/etc/ssl
    volumes:
      - ../deploy/certificates/staging/ca/ca.crt:/etc/ssl/ca/ca.crt:ro
      - ../deploy/certificates/staging/services/ffmpeg/server.crt:/etc/ssl/certs/server.crt:ro
      - ../deploy/certificates/staging/services/ffmpeg/server.key:/etc/ssl/private/server.key:ro

  audio-speak-dia-pyannote:
    environment:
      - ENABLE_MTLS=true
      - MTLS_CERT_DIR=/etc/ssl
    volumes:
      - ../deploy/certificates/staging/ca/ca.crt:/etc/ssl/ca/ca.crt:ro
      - ../deploy/certificates/staging/services/pyannote/server.crt:/etc/ssl/certs/server.crt:ro
      - ../deploy/certificates/staging/services/pyannote/server.key:/etc/ssl/private/server.key:ro

  open-parse:
    environment:
      - ENABLE_MTLS=true
      - MTLS_CERT_DIR=/etc/ssl
    volumes:
      - ../deploy/certificates/staging/ca/ca.crt:/etc/ssl/ca/ca.crt:ro
      - ../deploy/certificates/staging/services/open-parse/server.crt:/etc/ssl/certs/server.crt:ro
      - ../deploy/certificates/staging/services/open-parse/server.key:/etc/ssl/private/server.key:ro
