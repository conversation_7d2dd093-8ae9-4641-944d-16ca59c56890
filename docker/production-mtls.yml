name: divinci-production-mtls

networks:
  production-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16

services:
  # ========================================================================
  # API Servers
  # ========================================================================
  public-api:
    build:
      context: ..
      dockerfile: workspace/servers/public-api/Dockerfile
    working_dir: "/home/<USER>/app/servers/public-api"
    environment:
      - ENV_FOLDER=/home/<USER>/app/servers/public-api/env
      - ENABLE_MTLS=true
      - DEBUG_MTLS=false
      - MTLS_CERT_DIR=/etc/ssl
      - MTLS_VERIFY_SERVER=true
      - NODE_ENV=production
      - LOG_DEBUG=0
    volumes:
      - ../workspace:/home/<USER>/app
      - ../node_modules:/home/<USER>/node_modules
      - ../private-keys/production:/home/<USER>/app/servers/public-api/env
      # mTLS client certificates for outgoing connections
      - ../deploy/certificates/production/ca/ca.crt:/etc/ssl/ca/ca.crt:ro
      - ../deploy/certificates/production/services/public-api/client.crt:/etc/ssl/certs/client.crt:ro
      - ../deploy/certificates/production/services/public-api/client.key:/etc/ssl/private/client.key:ro
      # Additional path mappings for compatibility
      - ../deploy/certificates/production/ca/ca.crt:/etc/ssl/ca.crt:ro
      - ../deploy/certificates/production/services/public-api/client.crt:/etc/ssl/client.crt:ro
      - ../deploy/certificates/production/services/public-api/client.key:/etc/ssl/client.key:ro
    networks:
      - production-network
    ports:
      - "443:8080"
    depends_on:
      - audio-splitter-ffmpeg
      - audio-speak-dia-pyannote
      - open-parse
      - mongodb-production
      - redis-production
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8080/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # ========================================================================
  # Worker Services with mTLS
  # ========================================================================
  audio-splitter-ffmpeg:
    build:
      context: ../workspace/workers/audio-splitter@ffmpeg
      dockerfile: typescript.Dockerfile
    environment:
      - HTTP_PORT=5000
      - ENV_FOLDER=/home/<USER>/env
      - ENABLE_MTLS=true
      - DEBUG_MTLS=false
      - MTLS_CERT_DIR=/etc/ssl
      - MTLS_VERIFY_CLIENT=true
      - NODE_ENV=production
    volumes:
      - ../private-keys/production:/home/<USER>/env
      # mTLS server certificates for incoming connections
      - ../deploy/certificates/production/ca/ca.crt:/etc/ssl/ca/ca.crt:ro
      - ../deploy/certificates/production/services/ffmpeg/server.crt:/etc/ssl/certs/server.crt:ro
      - ../deploy/certificates/production/services/ffmpeg/server.key:/etc/ssl/private/server.key:ro
      # Additional path mappings for compatibility
      - ../deploy/certificates/production/ca/ca.crt:/etc/ssl/ca.crt:ro
      - ../deploy/certificates/production/services/ffmpeg/server.crt:/etc/ssl/server.crt:ro
      - ../deploy/certificates/production/services/ffmpeg/server.key:/etc/ssl/server.key:ro
    networks:
      - production-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-k", "https://localhost:5000/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  audio-speak-dia-pyannote:
    build:
      context: ../workspace/workers/audio-speaker-diarization@pyannote
      dockerfile: python.Dockerfile
    environment:
      - HTTP_PORT=5000
      - ENV_FOLDER=/home/<USER>/app/env
      - ENABLE_MTLS=true
      - DEBUG_MTLS=false
      - MTLS_CERT_DIR=/etc/ssl
      - MTLS_VERIFY_CLIENT=true
    volumes:
      - ../private-keys/production:/home/<USER>/app/env
      # mTLS server certificates for incoming connections
      - ../deploy/certificates/production/ca/ca.crt:/etc/ssl/ca/ca.crt:ro
      - ../deploy/certificates/production/services/pyannote/server.crt:/etc/ssl/certs/server.crt:ro
      - ../deploy/certificates/production/services/pyannote/server.key:/etc/ssl/private/server.key:ro
      # Additional path mappings for compatibility
      - ../deploy/certificates/production/ca/ca.crt:/etc/ssl/ca.crt:ro
      - ../deploy/certificates/production/services/pyannote/server.crt:/etc/ssl/server.crt:ro
      - ../deploy/certificates/production/services/pyannote/server.key:/etc/ssl/server.key:ro
    networks:
      - production-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-k", "https://localhost:5000/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  open-parse:
    build:
      context: ../workspace/workers/open-parse
      dockerfile: open-parse.Dockerfile
    environment:
      - HTTP_PORT=5000
      - ENV_FOLDER=/home/<USER>/app/env
      - ENABLE_MTLS=true
      - DEBUG_MTLS=false
      - MTLS_CERT_DIR=/etc/ssl
      - MTLS_VERIFY_CLIENT=true
      - PYTHONUNBUFFERED=1
    volumes:
      - ../private-keys/production:/home/<USER>/app/env
      # mTLS server certificates for incoming connections
      - ../deploy/certificates/production/ca/ca.crt:/etc/ssl/ca/ca.crt:ro
      - ../deploy/certificates/production/services/open-parse/server.crt:/etc/ssl/certs/server.crt:ro
      - ../deploy/certificates/production/services/open-parse/server.key:/etc/ssl/private/server.key:ro
      # Additional path mappings for compatibility
      - ../deploy/certificates/production/ca/ca.crt:/etc/ssl/ca.crt:ro
      - ../deploy/certificates/production/services/open-parse/server.crt:/etc/ssl/server.crt:ro
      - ../deploy/certificates/production/services/open-parse/server.key:/etc/ssl/server.key:ro
    networks:
      - production-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-k", "https://localhost:5000/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # ========================================================================
  # Database Services
  # ========================================================================
  mongodb-production:
    image: mongo:7.0
    environment:
      - MONGO_INITDB_ROOT_USERNAME=admin
      - MONGO_INITDB_ROOT_PASSWORD_FILE=/run/secrets/mongodb_password
    env_file:
      - ../private-keys/production/mongodb.env
    volumes:
      - mongodb_data:/data/db
      - mongodb_config:/data/configdb
    networks:
      - production-network
    restart: unless-stopped
    command: ["mongod", "--bind_ip_all", "--auth"]
    healthcheck:
      test: ["CMD", "mongosh", "--eval", "db.adminCommand('ping')"]
      interval: 30s
      timeout: 10s
      retries: 3

  redis-production:
    image: redis:7.2-alpine
    environment:
      - REDIS_PASSWORD_FILE=/run/secrets/redis_password
    env_file:
      - ../private-keys/production/redis.env
    volumes:
      - redis_data:/data
    networks:
      - production-network
    restart: unless-stopped
    command: ["redis-server", "--requirepass", "${REDIS_PASSWORD}"]
    healthcheck:
      test:
        [
          "CMD",
          "redis-cli",
          "--no-auth-warning",
          "-a",
          "${REDIS_PASSWORD}",
          "ping",
        ]
      interval: 30s
      timeout: 10s
      retries: 3

volumes:
  mongodb_data:
    driver: local
  mongodb_config:
    driver: local
  redis_data:
    driver: local

# Production secrets (use Docker secrets or external secret management)
secrets:
  mongodb_password:
    external: true
  redis_password:
    external: true
