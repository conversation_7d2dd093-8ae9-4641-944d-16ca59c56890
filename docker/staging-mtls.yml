name: divinci-staging-mtls

networks:
  staging-network:
    driver: bridge

services:
  # ========================================================================
  # API Servers
  # ========================================================================
  public-api:
    build:
      context: ..
      dockerfile: workspace/servers/public-api/Dockerfile
    working_dir: "/home/<USER>/app/servers/public-api"
    environment:
      - ENV_FOLDER=/home/<USER>/app/servers/public-api/env
      - ENABLE_MTLS=true
      - DEBUG_MTLS=true
      - MTLS_CERT_DIR=/etc/ssl
      - MTLS_VERIFY_SERVER=true
      - NODE_ENV=production
      - LOG_DEBUG=0
      - DEBUG=app,mtls
    volumes:
      - ../workspace:/home/<USER>/app
      - ../node_modules:/home/<USER>/node_modules
      - ../private-keys/staging:/home/<USER>/app/servers/public-api/env
      # mTLS client certificates for outgoing connections
      - ../deploy/certificates/staging/ca/ca.crt:/etc/ssl/ca/ca.crt:ro
      - ../deploy/certificates/staging/services/public-api/client.crt:/etc/ssl/certs/client.crt:ro
      - ../deploy/certificates/staging/services/public-api/client.key:/etc/ssl/private/client.key:ro
      # Additional path mappings for compatibility
      - ../deploy/certificates/staging/ca/ca.crt:/etc/ssl/ca.crt:ro
      - ../deploy/certificates/staging/services/public-api/client.crt:/etc/ssl/client.crt:ro
      - ../deploy/certificates/staging/services/public-api/client.key:/etc/ssl/client.key:ro
    networks:
      - staging-network
    ports:
      - "9443:8080"
    depends_on:
      - audio-splitter-ffmpeg
      - audio-speak-dia-pyannote
      - open-parse
      - mongodb-staging
      - redis-staging
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8080/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # ========================================================================
  # Worker Services with mTLS
  # ========================================================================
  audio-splitter-ffmpeg:
    build:
      context: ../workspace/workers/audio-splitter@ffmpeg
      dockerfile: typescript.Dockerfile
    environment:
      - HTTP_PORT=5000
      - ENV_FOLDER=/home/<USER>/env
      - ENABLE_MTLS=true
      - DEBUG_MTLS=true
      - MTLS_CERT_DIR=/etc/ssl
      - MTLS_VERIFY_CLIENT=true
      - NODE_ENV=production
    volumes:
      - ../private-keys/staging:/home/<USER>/env
      # mTLS server certificates for incoming connections
      - ../deploy/certificates/staging/ca/ca.crt:/etc/ssl/ca/ca.crt:ro
      - ../deploy/certificates/staging/services/ffmpeg/server.crt:/etc/ssl/certs/server.crt:ro
      - ../deploy/certificates/staging/services/ffmpeg/server.key:/etc/ssl/private/server.key:ro
    networks:
      - staging-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-k", "https://localhost:5000/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  audio-speak-dia-pyannote:
    build:
      context: ../workspace/workers/audio-speaker-diarization@pyannote
      dockerfile: python.Dockerfile
    environment:
      - HTTP_PORT=5000
      - ENV_FOLDER=/home/<USER>/app/env
      - ENABLE_MTLS=true
      - DEBUG_MTLS=true
      - MTLS_CERT_DIR=/etc/ssl
      - MTLS_VERIFY_CLIENT=true
    volumes:
      - ../private-keys/staging:/home/<USER>/app/env
      # mTLS server certificates for incoming connections
      - ../deploy/certificates/staging/ca/ca.crt:/etc/ssl/ca/ca.crt:ro
      - ../deploy/certificates/staging/services/pyannote/server.crt:/etc/ssl/certs/server.crt:ro
      - ../deploy/certificates/staging/services/pyannote/server.key:/etc/ssl/private/server.key:ro
    networks:
      - staging-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-k", "https://localhost:5000/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  open-parse:
    build:
      context: ../workspace/workers/open-parse
      dockerfile: open-parse.Dockerfile
    environment:
      - HTTP_PORT=5000
      - ENV_FOLDER=/home/<USER>/app/env
      - ENABLE_MTLS=true
      - DEBUG_MTLS=true
      - MTLS_CERT_DIR=/etc/ssl
      - MTLS_VERIFY_CLIENT=true
      - PYTHONUNBUFFERED=1
    volumes:
      - ../private-keys/staging:/home/<USER>/app/env
      # mTLS server certificates for incoming connections
      - ../deploy/certificates/staging/ca/ca.crt:/etc/ssl/ca/ca.crt:ro
      - ../deploy/certificates/staging/services/open-parse/server.crt:/etc/ssl/certs/server.crt:ro
      - ../deploy/certificates/staging/services/open-parse/server.key:/etc/ssl/private/server.key:ro
    networks:
      - staging-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-k", "https://localhost:5000/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # ========================================================================
  # Database Services
  # ========================================================================
  mongodb-staging:
    image: mongo:7.0
    env_file:
      - ../private-keys/staging/mongodb.env
    volumes:
      - mongodb_staging_data:/data/db
    networks:
      - staging-network
    restart: unless-stopped
    command: ["mongod", "--bind_ip_all", "--auth"]
    healthcheck:
      test: ["CMD", "mongosh", "--eval", "db.adminCommand('ping')"]
      interval: 30s
      timeout: 10s
      retries: 3

  redis-staging:
    image: redis:7.2-alpine
    env_file:
      - ../private-keys/staging/redis.env
    volumes:
      - redis_staging_data:/data
    networks:
      - staging-network
    restart: unless-stopped
    command: ["redis-server", "--requirepass", "${REDIS_PASSWORD}"]
    healthcheck:
      test:
        [
          "CMD",
          "redis-cli",
          "--no-auth-warning",
          "-a",
          "${REDIS_PASSWORD}",
          "ping",
        ]
      interval: 30s
      timeout: 10s
      retries: 3

volumes:
  mongodb_staging_data:
    driver: local
  redis_staging_data:
    driver: local
