import { DIVINCI_AUDIO_SPLITTER_FFMPEG_URL } from "./constants";
import { handleFetch, fetchBody } from "@divinci-ai/utils";
import { R2Pointer } from "./types";
import { HTTP_ERRORS_WITH_CONTEXT } from "@divinci-ai/server-utils";
import { mtlsFetch } from "./mtls-fetch";

export async function getAudioDuration(r2Pointer: R2Pointer) {
  try {
    return (await handleFetch(
      mtlsFetch(
        `${DIVINCI_AUDIO_SPLITTER_FFMPEG_URL}/get-duration`,
        fetchBody("POST", {
          Bucket: r2Pointer.Bucket,
          Key: r2Pointer.Key,
        })
      )
    )) as { duration: number };
  } catch (e) {
    console.error(
      `Failed to run ${DIVINCI_AUDIO_SPLITTER_FFMPEG_URL}/get-duration:`,
      e
    );
    throw HTTP_ERRORS_WITH_CONTEXT.SERVER_ERROR("Failed to get audio duration");
  }
}
