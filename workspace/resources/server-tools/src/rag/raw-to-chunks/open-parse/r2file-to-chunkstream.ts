// import { ElementsToTextChunks } from "./ElementsToTextChunks";
// import { logDebug } from "@divinci-ai/utils";
import { Readable } from "stream";
// import { Parser } from "stream-json";
// import StreamArray from "stream-json/streamers/StreamArray";
import { R2FileLocation, ChunkingConfig } from "../../types";

import { getWhitelabelVectorR2Instance } from "@divinci-ai/server-globals";
import { convertWebReadableToReadable } from "@divinci-ai/utils";

// First, create a helper function to convert Readable to Buffer
async function streamToBuffer(stream: Readable): Promise<Buffer> {
  const chunks: Buffer[] = [];
  for await (const chunk of stream) {
    chunks.push(Buffer.from(chunk));
  }
  return Buffer.concat(chunks);
}

export async function r2FileToChunkStream(
  r2File: R2FileLocation,
  config?: ChunkingConfig
): Promise<{ readable: Readable & { count: number }; cancel: () => void }> {
  const r2Target = {
    Bucket: r2File.bucket,
    Key: r2File.objectKey,
  };

  const r2 = getWhitelabelVectorR2Instance();
  const r2Obj = await r2.getObject(r2Target);

  if (!r2Obj.Body) {
    throw new Error("Could not retrieve file body from R2");
  }

  const bodyStream = convertWebReadableToReadable(
    r2Obj.Body.transformToWebStream()
  );
  const buffer = await streamToBuffer(bodyStream);

  // Check if this is likely a text file (simple heuristic)
  // This helps detect if we're processing the right content type
  const isLikelyText =
    buffer.length > 0 &&
    buffer
      .toString("utf8", 0, Math.min(100, buffer.length))
      .split("")
      .every((char) => char.charCodeAt(0) < 128); // ASCII check

  if (!isLikelyText) {
    console.warn(
      `⚠️ Warning: File content doesn't appear to be text. This might indicate we're processing the wrong file.`
    );

    // Check if this is an audio file by looking at the file extension
    const fileExt = r2File.originalName.split(".").pop()?.toLowerCase();
    const audioExtensions = [
      "mp3",
      "wav",
      "ogg",
      "flac",
      "aac",
      "m4a",
      "opus",
      "wma",
      "mp4",
      "mov",
      "webm",
    ];

    if (fileExt && audioExtensions.includes(fileExt)) {
      console.error(
        `❌ Attempted to process audio file (${r2File.originalName}) directly for RAG chunking`
      );
      throw new Error(
        `Cannot process audio file directly. The AI assistant should use the speaker-labeled transcript text file instead of the audio file (${fileExt}) for retrieval augmentation chunking.`
      );
    }
  } else {
    console.log(
      `✅ Processing text file (${r2File.originalName}) for RAG chunking`
    );
  }

  // Add configuration
  const openparseConfig = {
    minTokens: config?.openparse?.minTokens || 64,
    maxTokens: config?.openparse?.maxTokens || 1024,
    embeddings_provider:
      config?.openparse?.embeddings?.provider || "cloudflare",
  };

  // Ensure we use a .txt extension for the file sent to open-parse
  // This prevents open-parse from trying to process it as a video/audio file
  const textFilename = r2File.originalName.replace(/\.[^/.]+$/, "") + ".txt";

  console.log(
    `📄 Original filename: ${r2File.originalName}, sending as: ${textFilename}`
  );

  const formData = new FormData();
  formData.append("file", new Blob([buffer]), textFilename);
  formData.append("config", JSON.stringify(openparseConfig));

  // Use serviceFetch which automatically detects internal services and applies mTLS
  const openParseUrl = process.env.OPENPARSE_API_URL || "http://localhost:8084";
  const { serviceFetch } = await import("@divinci-ai/server-utils");

  const response = await serviceFetch(openParseUrl, {
    method: "POST",
    body: formData,
  });

  if (!response.ok) {
    const errorText = await response
      .text()
      .catch(() => "Could not read error response");
    console.error(
      `❌ OpenParse server error: ${response.status} ${response.statusText}`
    );
    console.error(`❌ Error details: ${errorText}`);
    throw new Error(
      `OpenParse server error: ${response.status} ${response.statusText} - ${errorText}`
    );
  }

  const readable = convertWebReadableToReadable(
    response.body as ReadableStream
  );
  (readable as any).count = 0; // Initialize count

  return {
    readable: readable as Readable & { count: number },
    cancel: () => {
      // Implement cancellation logic here
      readable.destroy();
    },
  };
}
