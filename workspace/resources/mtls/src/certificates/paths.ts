/**
 * Certificate path utilities for mTLS implementation
 */

import { join as pathJoin } from "path";
import { CertificateType, Environment, FileType } from "../types";

/**
 * Standard certificate paths
 */
export const STANDARD_PATHS = {
  // Server certificate paths
  SERVER_CERT_PATH: "/etc/ssl/certs/server.crt",
  SERVER_KEY_PATH: "/etc/ssl/private/server.key",

  // Client certificate paths
  CLIENT_CERT_PATH: "/etc/ssl/client/client.crt",
  CLIENT_KEY_PATH: "/etc/ssl/private/client.key",

  // CA certificate path
  CA_CERT_PATH: "/etc/ssl/ca/ca.crt",

  // Local development paths
  LOCAL_CERT_DIR: "/private-keys/local/certs/mtls",

  // Environment-specific paths
  ENVIRONMENT_CERT_DIR: (environment: Environment) =>
    `/private-keys/${environment}/certs/mtls`,
};

/**
 * Environment variable names for certificate paths
 */
export const ENV_VAR_NAMES = {
  CERT_DIR: "MTLS_CERT_DIR",
  CLIENT_CERT_PATH: "MTLS_CLIENT_CERT_PATH",
  CLIENT_KEY_PATH: "MTLS_CLIENT_KEY_PATH",
  SERVER_CERT_PATH: "MTLS_SERVER_CERT_PATH",
  SERVER_KEY_PATH: "MTLS_SERVER_KEY_PATH",
  CA_CERT_PATH: "MTLS_CA_CERT_PATH",
  ENABLE_MTLS: "ENABLE_MTLS",
  VERIFY_CLIENT: "MTLS_VERIFY_CLIENT",
  VERIFY_SERVER: "MTLS_VERIFY_SERVER",
};

/**
 * Get the standard path for a certificate or key
 *
 * @param certType Certificate type (server or client)
 * @param fileType File type (cert or key)
 * @param environment Environment (local, develop, staging, production)
 * @returns Standard path for the certificate or key
 */
export function getStandardPath(
  certType: CertificateType,
  fileType: FileType,
  environment?: Environment
): string {
  // If environment is specified, use environment-specific path
  if (environment) {
    const envDir = STANDARD_PATHS.ENVIRONMENT_CERT_DIR(environment);
    return pathJoin(
      envDir,
      `${certType}.${fileType === "cert" ? "crt" : "key"}`
    );
  }

  // Otherwise, use standard path
  if (certType === "server") {
    return fileType === "cert"
      ? STANDARD_PATHS.SERVER_CERT_PATH
      : STANDARD_PATHS.SERVER_KEY_PATH;
  } else {
    return fileType === "cert"
      ? STANDARD_PATHS.CLIENT_CERT_PATH
      : STANDARD_PATHS.CLIENT_KEY_PATH;
  }
}

/**
 * Get all possible paths for a certificate or key
 *
 * @param certType Certificate type (server or client)
 * @param fileType File type (cert or key)
 * @param environment Environment (local, develop, staging, production)
 * @returns Array of possible paths for the certificate or key
 */
export function getAllPossiblePaths(
  certType: CertificateType,
  fileType: FileType,
  environment?: Environment
): string[] {
  const paths: string[] = [];

  // Add standard path
  paths.push(getStandardPath(certType, fileType));

  // Add environment variable path if set
  const envVarName =
    certType === "server"
      ? fileType === "cert"
        ? ENV_VAR_NAMES.SERVER_CERT_PATH
        : ENV_VAR_NAMES.SERVER_KEY_PATH
      : fileType === "cert"
      ? ENV_VAR_NAMES.CLIENT_CERT_PATH
      : ENV_VAR_NAMES.CLIENT_KEY_PATH;

  const envVarPath = process.env[envVarName];
  if (envVarPath) {
    paths.push(envVarPath);
  }

  // Add path with custom cert directory if set
  const certDir = process.env[ENV_VAR_NAMES.CERT_DIR];
  if (certDir) {
    const fileName = `${certType}.${fileType === "cert" ? "crt" : "key"}`;
    const subDir = fileType === "cert" ? "certs" : "private";

    // Try multiple directory structures
    paths.push(pathJoin(certDir, subDir, fileName)); // Docker standard: /etc/ssl/certs/client.crt
    paths.push(pathJoin(certDir, fileName)); // Flat: /etc/ssl/client.crt
    paths.push(pathJoin(certDir, `services/public-api/${fileName}`)); // Service-specific

    // For client certificates, also try the standard client.crt/client.key names
    if (certType === "client") {
      const standardName = fileType === "cert" ? "client.crt" : "client.key";
      paths.push(pathJoin(certDir, subDir, standardName));
      paths.push(pathJoin(certDir, standardName));
    }

    // For server certificates, also try the standard server.crt/server.key names
    if (certType === "server") {
      const standardName = fileType === "cert" ? "server.crt" : "server.key";
      paths.push(pathJoin(certDir, subDir, standardName));
      paths.push(pathJoin(certDir, standardName));
    }
  }

  // Add local development path
  paths.push(
    pathJoin(
      STANDARD_PATHS.LOCAL_CERT_DIR,
      `${certType}.${fileType === "cert" ? "crt" : "key"}`
    )
  );

  // Add environment-specific path if environment is specified
  if (environment) {
    paths.push(getStandardPath(certType, fileType, environment));
  }

  // Add environment-specific paths for all environments if environment is not provided
  if (!environment) {
    const environments: Environment[] = [
      "local",
      "develop",
      "staging",
      "production",
    ];
    for (const env of environments) {
      paths.push(getStandardPath(certType, fileType, env));
    }
  }

  // Remove duplicates while preserving order
  return [...new Set(paths)];
}

/**
 * Resolve the actual path for a certificate or key
 *
 * @param certType Certificate type (server or client)
 * @param fileType File type (cert or key)
 * @param environment Environment (local, develop, staging, production)
 * @param fs File system module (for testing)
 * @returns Resolved path for the certificate or key, or null if not found
 */
export function resolveCertificatePath(
  certType: CertificateType,
  fileType: FileType,
  environment?: Environment,
  fs: { existsSync: (path: string) => boolean } = require("fs")
): string | null {
  // Get all possible paths
  const paths = getAllPossiblePaths(certType, fileType, environment);

  // Find the first path that exists
  for (const path of paths) {
    if (fs.existsSync(path)) {
      return path;
    }
  }

  // No path found
  return null;
}

/**
 * Get the CA certificate path with enhanced path resolution
 *
 * @param environment Environment (local, develop, staging, production)
 * @param fs File system module (for testing)
 * @returns Resolved path for the CA certificate, or null if not found
 */
export function getCAPath(
  environment?: Environment,
  fs: { existsSync: (path: string) => boolean } = require("fs")
): string | null {
  const paths: string[] = [];

  // Add environment variable path first (highest priority)
  const envVarPath = process.env[ENV_VAR_NAMES.CA_CERT_PATH];
  if (envVarPath) {
    paths.push(envVarPath);
  }

  // Add path with custom cert directory if set
  const certDir = process.env[ENV_VAR_NAMES.CERT_DIR];
  if (certDir) {
    // Try multiple subdirectory structures
    paths.push(pathJoin(certDir, "ca/ca.crt")); // Docker standard structure
    paths.push(pathJoin(certDir, "ca.crt")); // Flat structure
    paths.push(pathJoin(certDir, "certs/ca.crt")); // Alternative structure
  }

  // Add standard path
  paths.push(STANDARD_PATHS.CA_CERT_PATH);

  // Add local development path
  paths.push(pathJoin(STANDARD_PATHS.LOCAL_CERT_DIR, "ca/ca.crt"));

  // Add environment-specific paths if environment is specified
  if (environment) {
    const envDir = STANDARD_PATHS.ENVIRONMENT_CERT_DIR(environment);
    paths.push(pathJoin(envDir, "ca/ca.crt"));
    paths.push(pathJoin(envDir, "ca.crt"));
  }

  // Add fallback paths for different environments
  if (!environment) {
    const environments: Environment[] = [
      "local",
      "develop",
      "staging",
      "production",
    ];
    for (const env of environments) {
      const envDir = STANDARD_PATHS.ENVIRONMENT_CERT_DIR(env);
      paths.push(pathJoin(envDir, "ca/ca.crt"));
    }
  }

  // Find the first path that exists
  for (const path of paths) {
    if (fs.existsSync(path)) {
      return path;
    }
  }

  // No path found
  return null;
}
