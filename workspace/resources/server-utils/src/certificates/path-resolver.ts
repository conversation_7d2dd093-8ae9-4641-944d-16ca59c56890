/**
 * Comprehensive certificate path resolver for mTLS implementation
 * Handles multiple path structures and environments
 */

import { existsSync } from "fs";
import { join as pathJoin } from "path";

export type Environment = "local" | "staging" | "production" | "development";
export type CertificateType = "ca" | "client" | "server";
export type FileType = "cert" | "key";

/**
 * Certificate path configuration
 */
export interface CertificatePathConfig {
  environment?: Environment;
  certDir?: string;
  serviceName?: string;
  debug?: boolean;
}

/**
 * Certificate file paths
 */
export interface CertificatePaths {
  ca: string | null;
  clientCert: string | null;
  clientKey: string | null;
  serverCert: string | null;
  serverKey: string | null;
}

/**
 * Standard certificate directory structures
 */
const CERT_STRUCTURES = {
  // Docker production structure: /etc/ssl/ca/ca.crt, /etc/ssl/certs/client.crt
  DOCKER_STANDARD: {
    ca: "ca/ca.crt",
    clientCert: "certs/client.crt",
    clientKey: "private/client.key",
    serverCert: "certs/server.crt",
    serverKey: "private/server.key",
  },

  // Flat structure: /etc/ssl/ca.crt, /etc/ssl/client.crt
  FLAT: {
    ca: "ca.crt",
    clientCert: "client.crt",
    clientKey: "client.key",
    serverCert: "server.crt",
    serverKey: "server.key",
  },

  // Service-specific structure: /etc/ssl/services/public-api/client.crt
  SERVICE_SPECIFIC: {
    ca: "ca/ca.crt",
    clientCert: "services/{service}/client.crt",
    clientKey: "services/{service}/client.key",
    serverCert: "services/{service}/server.crt",
    serverKey: "services/{service}/server.key",
  },

  // Development structure: /private-keys/local/certs/mtls/...
  DEVELOPMENT: {
    ca: "ca/ca.crt",
    clientCert: "services/{service}/client.crt",
    clientKey: "services/{service}/client.key",
    serverCert: "services/{service}/server.crt",
    serverKey: "services/{service}/server.key",
  },
};

/**
 * Get base certificate directory from environment
 */
function getBaseCertDir(config: CertificatePathConfig): string {
  // 1. Use explicit certDir if provided
  if (config.certDir) {
    return config.certDir;
  }

  // 2. Use MTLS_CERT_DIR environment variable
  if (process.env.MTLS_CERT_DIR) {
    return process.env.MTLS_CERT_DIR;
  }

  // 3. Use environment-specific defaults
  const environment = config.environment || process.env.ENVIRONMENT || "local";

  switch (environment) {
    case "production":
    case "staging":
      return "/etc/ssl";
    case "local":
    case "development":
    default:
      return `./private-keys/${environment}/certs/mtls`;
  }
}

/**
 * Get service name for path resolution
 */
function getServiceName(config: CertificatePathConfig): string {
  if (config.serviceName) {
    return config.serviceName;
  }

  // Try to detect service name from environment
  if (process.env.SERVICE_NAME) {
    return process.env.SERVICE_NAME;
  }

  // Default service names based on common patterns
  if (process.env.npm_package_name) {
    const packageName = process.env.npm_package_name;
    if (packageName.includes("public-api")) return "public-api";
    if (packageName.includes("ffmpeg")) return "ffmpeg";
    if (packageName.includes("pyannote")) return "pyannote";
    if (packageName.includes("open-parse")) return "open-parse";
  }

  // Default to public-api for client certificates
  return "public-api";
}

/**
 * Generate all possible paths for a certificate file
 */
function generatePossiblePaths(
  baseDir: string,
  certType: CertificateType,
  fileType: FileType,
  serviceName: string
): string[] {
  const paths: string[] = [];

  // Get the file pattern for each structure
  Object.values(CERT_STRUCTURES).forEach((structure) => {
    let pattern: string;

    switch (certType) {
      case "ca":
        pattern = structure.ca;
        break;
      case "client":
        pattern =
          fileType === "cert" ? structure.clientCert : structure.clientKey;
        break;
      case "server":
        pattern =
          fileType === "cert" ? structure.serverCert : structure.serverKey;
        break;
    }

    // Replace {service} placeholder
    pattern = pattern.replace("{service}", serviceName);

    // Create full path
    const fullPath = pathJoin(baseDir, pattern);
    paths.push(fullPath);
  });

  // Add environment variable overrides
  const envVarMap = {
    ca: "MTLS_CA_CERT_PATH",
    clientCert: "MTLS_CLIENT_CERT_PATH",
    clientKey: "MTLS_CLIENT_KEY_PATH",
    serverCert: "MTLS_SERVER_CERT_PATH",
    serverKey: "MTLS_SERVER_KEY_PATH",
  };

  const key =
    certType === "ca"
      ? "ca"
      : certType === "client"
      ? fileType === "cert"
        ? "clientCert"
        : "clientKey"
      : fileType === "cert"
      ? "serverCert"
      : "serverKey";

  const envVar = envVarMap[key as keyof typeof envVarMap];
  if (process.env[envVar]) {
    paths.unshift(process.env[envVar]!); // Add to beginning for priority
  }

  // Remove duplicates while preserving order
  return [...new Set(paths)];
}

/**
 * Find the first existing path from a list of possible paths
 */
function findExistingPath(paths: string[], debug?: boolean): string | null {
  for (const path of paths) {
    if (debug) {
      console.log(`🔍 Checking certificate path: ${path}`);
    }

    if (existsSync(path)) {
      if (debug) {
        console.log(`✅ Found certificate at: ${path}`);
      }
      return path;
    }
  }

  if (debug) {
    console.log(`❌ No certificate found in any of the checked paths`);
  }

  return null;
}

/**
 * Resolve certificate paths for mTLS configuration
 */
export function resolveCertificatePaths(
  config: CertificatePathConfig = {}
): CertificatePaths {
  const baseDir = getBaseCertDir(config);
  const serviceName = getServiceName(config);
  const debug = config.debug || process.env.DEBUG_MTLS === "true";

  if (debug) {
    console.log(`🔐 Resolving certificate paths:`);
    console.log(`   Base directory: ${baseDir}`);
    console.log(`   Service name: ${serviceName}`);
    console.log(
      `   Environment: ${
        config.environment || process.env.ENVIRONMENT || "local"
      }`
    );
  }

  const result: CertificatePaths = {
    ca: null,
    clientCert: null,
    clientKey: null,
    serverCert: null,
    serverKey: null,
  };

  // Resolve CA certificate
  const caPaths = generatePossiblePaths(baseDir, "ca", "cert", serviceName);
  result.ca = findExistingPath(caPaths, debug);

  // Resolve client certificate and key
  const clientCertPaths = generatePossiblePaths(
    baseDir,
    "client",
    "cert",
    serviceName
  );
  result.clientCert = findExistingPath(clientCertPaths, debug);

  const clientKeyPaths = generatePossiblePaths(
    baseDir,
    "client",
    "key",
    serviceName
  );
  result.clientKey = findExistingPath(clientKeyPaths, debug);

  // Resolve server certificate and key
  const serverCertPaths = generatePossiblePaths(
    baseDir,
    "server",
    "cert",
    serviceName
  );
  result.serverCert = findExistingPath(serverCertPaths, debug);

  const serverKeyPaths = generatePossiblePaths(
    baseDir,
    "server",
    "key",
    serviceName
  );
  result.serverKey = findExistingPath(serverKeyPaths, debug);

  if (debug) {
    console.log(`🔐 Certificate resolution results:`);
    console.log(`   CA: ${result.ca || "NOT FOUND"}`);
    console.log(`   Client Cert: ${result.clientCert || "NOT FOUND"}`);
    console.log(`   Client Key: ${result.clientKey || "NOT FOUND"}`);
    console.log(`   Server Cert: ${result.serverCert || "NOT FOUND"}`);
    console.log(`   Server Key: ${result.serverKey || "NOT FOUND"}`);
  }

  return result;
}

/**
 * Check if mTLS is properly configured
 */
export function isMTLSConfigured(config: CertificatePathConfig = {}): boolean {
  const paths = resolveCertificatePaths(config);

  // For client mTLS, we need CA, client cert, and client key
  const hasClientMTLS = !!(paths.ca && paths.clientCert && paths.clientKey);

  // For server mTLS, we need CA, server cert, and server key
  const hasServerMTLS = !!(paths.ca && paths.serverCert && paths.serverKey);

  return hasClientMTLS || hasServerMTLS;
}

/**
 * Get mTLS configuration for use with HTTPS agents
 */
export function getMTLSConfig(config: CertificatePathConfig = {}): {
  ca?: string;
  cert?: string;
  key?: string;
  clientCert?: string;
  clientKey?: string;
  serverCert?: string;
  serverKey?: string;
} | null {
  const paths = resolveCertificatePaths(config);

  if (!paths.ca) {
    return null;
  }

  const result: any = {
    ca: paths.ca,
  };

  // Add client certificates if available
  if (paths.clientCert && paths.clientKey) {
    result.cert = paths.clientCert;
    result.key = paths.clientKey;
    result.clientCert = paths.clientCert;
    result.clientKey = paths.clientKey;
  }

  // Add server certificates if available
  if (paths.serverCert && paths.serverKey) {
    result.serverCert = paths.serverCert;
    result.serverKey = paths.serverKey;
  }

  return result;
}
