app-engine-go: google-cloud-sdk-app-engine-go
app-engine-grpc: google-cloud-sdk-app-engine-grpc
app-engine-java: google-cloud-sdk-app-engine-java
app-engine-php: unavailable
app-engine-python: google-cloud-sdk-app-engine-python
app-engine-python-extras: google-cloud-sdk-app-engine-python-extras
alpha: google-cloud-sdk
anthoscli: google-cloud-sdk
anthos-auth: google-cloud-sdk-anthos-auth
appctl: unavailable
beta: google-cloud-sdk
bigtable: google-cloud-sdk-bigtable-emulator
bq: google-cloud-sdk
cbt: google-cloud-sdk-cbt
cloud-build-local: google-cloud-sdk-cloud-build-local
cloud-run-proxy: google-cloud-sdk-cloud-run-proxy
cloud_sql_proxy: unavailable
cloud-sql-proxy: unavailable
run-compose: google-cloud-sdk-run-compose
cloud-datastore-emulator: google-cloud-sdk-datastore-emulator
cloud-firestore-emulator: google-cloud-sdk-firestore-emulator
cloud-spanner-emulator: google-cloud-sdk-spanner-emulator
core: google-cloud-sdk
pkg: unavailable
docker-credential-gcr: google-cloud-cli-docker-credential-gcr
enterprise-certificate-proxy: google-cloud-sdk-enterprise-certificate-proxy
gcloud: google-cloud-sdk
gcloud-crc32c: google-cloud-sdk
gke-gcloud-auth-plugin: google-cloud-sdk-gke-gcloud-auth-plugin
gsutil: google-cloud-sdk
spanner-migration-tool: google-cloud-sdk-spanner-migration-tool
istioctl: google-cloud-sdk-istioctl
kpt: google-cloud-sdk-kpt
kubectl: kubectl
kubectl-oidc: google-cloud-sdk-kubectl-oidc
log-streaming: google-cloud-sdk-log-streaming
local-extract: google-cloud-sdk-local-extract
package-go-module: google-cloud-sdk-package-go-module
minikube: google-cloud-sdk-minikube
nomos: google-cloud-sdk-nomos
pubsub-emulator: google-cloud-sdk-pubsub-emulator
skaffold: google-cloud-sdk-skaffold
kustomize: unavailable
config-connector: google-cloud-sdk-config-connector
terraform-tools: google-cloud-sdk-terraform-tools
bundled-python3-unix: google-cloud-sdk-bundled-python3
managed-flink-client: google-cloud-sdk-managed-flink-client
spanner-cli: google-cloud-cli-spanner-cli
