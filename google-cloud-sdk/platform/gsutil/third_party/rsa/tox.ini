[tox]
# Environment changes have to be manually synced with '.travis.yml'.
envlist = py27,py35,py36,p37,pypy

[pytest]
addopts = -v --cov rsa --cov-report term-missing

[testenv]
deps = pipenv
commands=
    pipenv install --dev
    pipenv run py.test tests

[testenv:py36]
commands=
    pipenv install --dev --ignore-pipfile
    pipenv run py.test --doctest-modules rsa tests

[pep8]
max-line-length = 100
