language: python
cache: pip

# Environment changes have to be manually synced with 'tox.ini'.
# See: https://github.com/travis-ci/travis-ci/issues/3024

# Python 3.7 is not yet supported by Travis <PERSON>.
# See: https://github.com/travis-ci/travis-ci/issues/9815

python:
  - "2.7"
  - "3.5"
  - "3.6"
  - "3.7-dev"

# This is blocked by https://github.com/pypa/pipenv/issues/2449,
# also see https://github.com/pypa/pipenv/projects/7
#  - "pypy"
  - "pypy3.5"

install:
  - pip install pipenv
  - pipenv install --dev

script:
  - pipenv run py.test

after_success:
  - pipenv run coveralls
