Python-RSA changelog
========================================

Version 4.3 & 4.5 - released 2020-06-12
----------------------------------------

Version 4.3 and 4.5 are almost a re-tagged release of version 4.0. It is the
last to support Python 2.7. This is now made explicit in the `python_requires`
argument in `setup.py`. Python 3.4 is not supported by this release. There was a
mistake releasing 4.4 as "3.5+ only", which made it necessary to retag 4.3 as
4.5 as well.

Two security fixes have also been backported, so 4.3 = 4.0 + these two fixes.

- Choose blinding factor relatively prime to <PERSON>. <PERSON> for pointing this out.
- Reject cyphertexts (when decrypting) and signatures (when verifying) that have
  been modified by prepending zero bytes. This resolves CVE-2020-13757. Thanks
  <PERSON> for pointing this out.


Version 4.0 - released 2018-09-16
----------------------------------------

- Removed deprecated modules:
    - rsa.varblock
    - rsa.bigfile
    - rsa._version133
    - rsa._version200
- Removed CLI commands that use the VARBLOCK/bigfile format.
- Ensured that PublicKey.save_pkcs1() and PrivateKey.save_pkcs1() always return bytes.
- Dropped support for Python 2.6 and 3.3.
- Dropped support for Psyco.
- Miller-Rabin iterations determined by bitsize of key.
  [#58](https://github.com/sybrenstuvel/python-rsa/pull/58)
- Added function `rsa.find_signature_hash()` to return the name of the hashing
  algorithm used to sign a message. `rsa.verify()` now also returns that name,
  instead of always returning `True`.
  [#78](https://github.com/sybrenstuvel/python-rsa/issues/13)
- Add support for SHA-224 for PKCS1 signatures.
  [#104](https://github.com/sybrenstuvel/python-rsa/pull/104)
- Transitioned from `requirements.txt` to Pipenv for package management.


Version 3.4.2 - released 2016-03-29
----------------------------------------

- Fixed dates in CHANGELOG.txt


Version 3.4.1 - released 2016-03-26
----------------------------------------

- Included tests/private.pem in MANIFEST.in
- Included README.md and CHANGELOG.txt in MANIFEST.in


Version 3.4 - released 2016-03-17
----------------------------------------

- Moved development to GitHub: https://github.com/sybrenstuvel/python-rsa
- Solved side-channel vulnerability by implementing blinding, fixes #19
- Deprecated the VARBLOCK format and rsa.bigfile module due to security issues, see
    https://github.com/sybrenstuvel/python-rsa/issues/13
- Integration with Travis-CI [1], Coveralls [2] and Code Climate [3]
- Deprecated the old rsa._version133 and rsa._version200 submodules, they will be
  completely removed in version 4.0.
- Add an 'exponent' argument to key.newkeys()
- Switched from Solovay-Strassen to Miller-Rabin primality testing, to
  comply with NIST FIPS 186-4 [4] as probabilistic primality test
  (Appendix C, subsection C.3):
- Fixed bugs #12, #14, #27, #30, #49

[1] https://travis-ci.org/sybrenstuvel/python-rsa
[2] https://coveralls.io/github/sybrenstuvel/python-rsa
[3] https://codeclimate.com/github/sybrenstuvel/python-rsa
[4] http://nvlpubs.nist.gov/nistpubs/FIPS/NIST.FIPS.186-4.pdf


Version 3.3 - released 2016-01-13
----------------------------------------

- Thanks to Filippo Valsorda: Fix BB'06 attack in verify() by
  switching from parsing to comparison. See [1] for more information.
- Simplified Tox configuration and dropped Python 3.2 support. The
  coverage package uses a u'' prefix, which was reintroduced in 3.3
  for ease of porting.

[1] https://blog.filippo.io/bleichenbacher-06-signature-forgery-in-python-rsa/


Version 3.2.3 - released 2015-11-05
----------------------------------------

- Added character encoding markers for Python 2.x


Version 3.2.1 - released 2015-11-05
----------------------------------------

- Added per-file licenses
- Added support for wheel packages
- Made example code more consistent and up to date with Python 3.4


Version 3.2 - released 2015-07-29
----------------------------------------

- Mentioned support for Python 3 in setup.py


Version 3.1.4 - released 2014-02-22
----------------------------------------

- Fixed some bugs


Version 3.1.3 - released 2014-02-02
----------------------------------------

- Dropped support for Python 2.5


Version 3.1.2 - released 2013-09-15
----------------------------------------

- Added Python 3.3 to the test environment.
- Removed dependency on Distribute
- Added support for loading public keys from OpenSSL


Version 3.1.1 - released 2012-06-18
----------------------------------------

- Fixed doctests for Python 2.7
- Removed obsolete unittest so all tests run fine on Python 3.2

Version 3.1 - released 2012-06-17
----------------------------------------

- Big, big credits to Yesudeep Mangalapilly for all the changes listed
  below!
- Added ability to generate keys on multiple cores simultaneously.
- Massive speedup
- Partial Python 3.2 compatibility (core functionality works, but
  saving or loading keys doesn't, for that the pyasn1 package needs to
  be ported to Python 3 first)
- Lots of bug fixes



Version 3.0.1 - released 2011-08-07
----------------------------------------

- Removed unused import of abc module


Version 3.0 - released 2011-08-05
----------------------------------------

- Changed the meaning of the keysize to mean the size of ``n`` rather than
  the size of both ``p`` and ``q``. This is the common interpretation of
  RSA keysize. To get the old behaviour, double the keysize when generating a
  new key.

- Added a lot of doctests

- Added random-padded encryption and decryption using PKCS#1 version 1.5

- Added hash-based signatures and verification using PKCS#1v1.5

- Modeling private and public key as real objects rather than dicts.

- Support for saving and loading keys as PEM and DER files.

- Ability to extract a public key from a private key (PEM+DER)


Version 2.0
----------------------------------------

- Security improvements by Barry Mead.
