name: Downstream

on: [push, pull_request, workflow_dispatch]

permissions: "read-all"

jobs:
  downstream:
    strategy:
      fail-fast: false
      matrix:
        downstream: [botocore, requests]
    runs-on: ubuntu-22.04
    timeout-minutes: 30

    steps:
      - name: "Checkout repository"
        uses: actions/checkout@0ad4b8fadaa221de15dcec353f45205ec38ea70b # v4.1.4

      - name: "Setup Python"
        uses: actions/setup-python@82c7e631bb3cdc910f68e0081d67478d79c6982d # v5.1.0
        with:
          python-version: "3.x"

      - name: "Install dependencies"
        run: python -m pip install --upgrade nox

      - name: "Run downstream tests"
        run: nox -s downstream_${{ matrix.downstream }}
