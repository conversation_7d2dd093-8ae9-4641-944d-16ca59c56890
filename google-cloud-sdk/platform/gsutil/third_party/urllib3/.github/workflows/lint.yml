name: lint

on: [push, pull_request, workflow_dispatch]

permissions: "read-all"

jobs:
  lint:
    runs-on: ubuntu-20.04
    timeout-minutes: 10

    steps:
      - name: "Checkout repository"
        uses: actions/checkout@0ad4b8fadaa221de15dcec353f45205ec38ea70b # v4.1.4

      - name: "Setup Python"
        uses: actions/setup-python@82c7e631bb3cdc910f68e0081d67478d79c6982d # v5.1.0
        with:
          python-version: "3.x"
          cache: pip

      - name: "Run pre-commit"
        uses: pre-commit/action@646c83fcd040023954eafda54b4db0192ce70507 # v3.0.0

      - name: "Install dependencies"
        run: python -m pip install nox

      - name: "Run mypy"
        run: nox -s mypy
