name: Publish to Py<PERSON>

on:
  push:
    tags:
      - "*"

permissions:
  contents: read

jobs:
  build:
    name: "Build dists"
    runs-on: "ubuntu-latest"
    environment:
      name: "publish"
    outputs:
      hashes: ${{ steps.hash.outputs.hashes }}

    steps:
      - name: "Checkout repository"
        uses: actions/checkout@0ad4b8fadaa221de15dcec353f45205ec38ea70b # v4.1.4

      - name: "Setup Python"
        uses: actions/setup-python@82c7e631bb3cdc910f68e0081d67478d79c6982d # v5.1.0
        with:
          python-version: "3.x"

      - name: "Install dependencies"
        run: python -m pip install build==0.8.0

      - name: "Build dists"
        run: |
          SOURCE_DATE_EPOCH=$(git log -1 --pretty=%ct) \
          python -m build

      - name: "Generate hashes"
        id: hash
        run: |
          cd dist && echo "hashes=$(sha256sum * | base64 -w0)" >> $GITHUB_OUTPUT

      - name: "Upload dists"
        uses: actions/upload-artifact@0b7f8abb1508181956e8e162db84b466c27e18ce # v3.1.2
        with:
          name: "dist"
          path: "dist/"
          if-no-files-found: error
          retention-days: 5

  provenance:
    needs: [build]
    permissions:
      actions: read
      contents: write
      id-token: write # Needed to access the workflow's OIDC identity.
    uses: slsa-framework/slsa-github-generator/.github/workflows/generator_generic_slsa3.yml@v1.10.0
    with:
      base64-subjects: "${{ needs.build.outputs.hashes }}"
      upload-assets: true
      compile-generator: true # Workaround for https://github.com/slsa-framework/slsa-github-generator/issues/1163

  publish:
    name: "Publish"
    if: startsWith(github.ref, 'refs/tags/')
    needs: ["build", "provenance"]
    permissions:
      contents: write
      id-token: write # Needed for trusted publishing to PyPI.
    runs-on: "ubuntu-latest"

    steps:
    - name: "Download dists"
      uses: actions/download-artifact@9bc31d5ccc31df68ecc42ccf4149144866c47d8a # v3.0.2
      with:
        name: "dist"
        path: "dist/"

    - name: "Upload dists to GitHub Release"
      env:
        GITHUB_TOKEN: "${{ secrets.GITHUB_TOKEN }}"
      run: |
        gh release upload ${{ github.ref_name }} dist/* --repo ${{ github.repository }}

    - name: "Publish dists to PyPI"
      uses: pypa/gh-action-pypi-publish@f8c70e705ffc13c3b4d1221169b84f12a75d6ca8 # v1.8.8
