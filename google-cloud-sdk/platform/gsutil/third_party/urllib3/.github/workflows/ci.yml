name: CI

on: [push, pull_request, workflow_dispatch]

permissions: "read-all"

defaults:
  run:
    shell: bash

jobs:
  package:
    runs-on: ubuntu-latest
    timeout-minutes: 10

    steps:
      - name: "Checkout repository"
        uses: actions/checkout@0ad4b8fadaa221de15dcec353f45205ec38ea70b # v4.1.4

      - name: "Setup Python"
        uses: actions/setup-python@82c7e631bb3cdc910f68e0081d67478d79c6982d # v5.1.0
        with:
          python-version: "3.x"
          cache: "pip"

      - name: "Check packages"
        run: |
          python -m pip install -U pip setuptools wheel build twine rstcheck
          python -m build
          rstcheck CHANGES.rst
          python -m twine check dist/*

  test:
    strategy:
      fail-fast: false
      matrix:
        python-version: ["3.8", "3.9", "3.10", "3.11", "3.12", "3.13"]
        os:
          - macos-12
          - windows-latest
          - ubuntu-22.04
        nox-session: ['']
        include:
          - experimental: false
          # integration
          # 3.8 and 3.9 have a known issue with large SSL requests that we work around:
          # https://github.com/urllib3/urllib3/pull/3181#issuecomment-1794830698
          - python-version: "3.8"
            os: ubuntu-latest
            experimental: false
            nox-session: test_integration
          - python-version: "3.9"
            os: ubuntu-latest
            experimental: false
            nox-session: test_integration
          - python-version: "3.12"
            os: ubuntu-latest
            experimental: false
            nox-session: test_integration
          # OpenSSL 1.1.1
          - python-version: "3.8"
            os: ubuntu-20.04
            experimental: false
            nox-session: test-3.8
          # pypy
          - python-version: "pypy-3.8"
            os: ubuntu-latest
            experimental: false
            nox-session: test-pypy3.8
          - python-version: "pypy-3.9-v7.3.13"
            os: ubuntu-latest
            experimental: false
            nox-session: test-pypy3.9
          - python-version: "pypy-3.10"
            os: ubuntu-latest
            experimental: false
            nox-session: test-pypy3.10
          - python-version: "3.x"
          # brotli
            os: ubuntu-latest
            experimental: false
            nox-session: test_brotlipy
          # Test CPython with a broken hostname_checks_common_name (the fix is in 3.9.3)
          - python-version: "3.9.2"
            os: ubuntu-20.04  # CPython 3.9.2 is not available for ubuntu-22.04.
            experimental: false
            nox-session: test-3.9
          - python-version: "3.11"
            os: ubuntu-latest
            nox-session: emscripten
            experimental: true
          - python-version: "3.13"
            experimental: true
        exclude:
          # Ubuntu 22.04 comes with OpenSSL 3.0, so only CPython 3.9+ is compatible with it
          # https://github.com/python/cpython/issues/83001
          - python-version: "3.8"
            os: ubuntu-22.04

    runs-on: ${{ matrix.os }}
    name: ${{ fromJson('{"macos-12":"macOS","windows-latest":"Windows","ubuntu-latest":"Ubuntu","ubuntu-20.04":"Ubuntu 20.04 (OpenSSL 1.1.1)","ubuntu-22.04":"Ubuntu 22.04 (OpenSSL 3.0)"}')[matrix.os] }} ${{ matrix.python-version }} ${{ matrix.nox-session}}
    continue-on-error: ${{ matrix.experimental }}
    timeout-minutes: 30
    steps:
      - name: "Checkout repository"
        uses: actions/checkout@0ad4b8fadaa221de15dcec353f45205ec38ea70b # v4.1.4

      - name: "Setup Python ${{ matrix.python-version }}"
        uses: actions/setup-python@82c7e631bb3cdc910f68e0081d67478d79c6982d # v5.1.0
        with:
          python-version: ${{ matrix.python-version }}
          allow-prereleases: true

      - name: "Install dependencies"
        run: python -m pip install --upgrade pip setuptools nox

      - name: "Install Chrome"
        uses: browser-actions/setup-chrome@db1b524c26f20a8d1a10f7fc385c92387e2d0477 # v1.7.1
        if: ${{ matrix.nox-session == 'emscripten' }}
      - name: "Install Firefox"
        uses: browser-actions/setup-firefox@233224b712fc07910ded8c15fb95a555c86da76f # v1.5.0
        if: ${{ matrix.nox-session == 'emscripten' }}
      - name: "Run tests"
        # If no explicit NOX_SESSION is set, run the default tests for the chosen Python version
        run: nox -s ${NOX_SESSION:-test-$PYTHON_VERSION}
        env:
          PYTHON_VERSION: ${{ matrix.python-version }}
          NOX_SESSION: ${{ matrix.nox-session }}

      - name: "Upload coverage data"
        uses: actions/upload-artifact@65462800fd760344b1a7b4382951275a0abb4808 # v4.3.3
        with:
          name: coverage-data-${{ matrix.python-version }}-${{ matrix.os }}-${{ matrix.experimental }}-${{ matrix.nox-session }}
          path: ".coverage.*"
          if-no-files-found: error


  coverage:
    if: always()
    runs-on: "ubuntu-latest"
    needs: test
    steps:
      - name: "Checkout repository"
        uses: actions/checkout@0ad4b8fadaa221de15dcec353f45205ec38ea70b # v4.1.4

      - name: "Setup Python"
        uses: actions/setup-python@82c7e631bb3cdc910f68e0081d67478d79c6982d # v5.1.0
        with:
          python-version: "3.x"

      - name: "Install coverage"
        run: "python -m pip install -r dev-requirements.txt"

      - name: "Download coverage data"
        uses: actions/download-artifact@65a9edc5881444af0b9093a5e628f2fe47ea3b2e # v4.1.7
        with:
          pattern: coverage-data-*
          merge-multiple: true

      - name: "Combine & check coverage"
        run: |
          python -m coverage combine
          python -m coverage html --skip-covered --skip-empty
          python -m coverage report --ignore-errors --show-missing --fail-under=100

      - if: ${{ failure() }}
        name: "Upload report if check failed"
        uses: actions/upload-artifact@65462800fd760344b1a7b4382951275a0abb4808 # v4.3.3
        with:
          name: coverage-report
          path: htmlcov
