name: "CodeQL"

on:
  push:
    branches: ["main"]
  pull_request:
    branches: ["main"]
  schedule:
    - cron: "0 0 * * 5"
  workflow_dispatch:

permissions: "read-all"

jobs:
  analyze:
    if: github.repository_owner == 'urllib3'
    name: "Analyze"
    runs-on: "ubuntu-latest"
    permissions:
      actions: read
      contents: read
      security-events: write
    steps:
    - name: "Checkout repository"
      uses: actions/checkout@0ad4b8fadaa221de15dcec353f45205ec38ea70b # v4.1.4

    - name: "Run CodeQL init"
      uses: github/codeql-action/init@9fdb3e49720b44c48891d036bb502feb25684276 # v3.25.6
      with:
        config-file: "./.github/codeql.yml"
        languages: "python"

    - name: "Run CodeQL autobuild"
      uses: github/codeql-action/autobuild@9fdb3e49720b44c48891d036bb502feb25684276 # v3.25.6

    - name: "Run CodeQL analyze"
      uses: github/codeql-action/analyze@9fdb3e49720b44c48891d036bb502feb25684276 # v3.25.6
