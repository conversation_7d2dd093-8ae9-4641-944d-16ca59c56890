h2==4.1.0
coverage==7.4.1
PySocks==1.7.1
pytest==8.0.2
pytest-timeout==2.1.0
pyOpenSSL==24.0.0
idna==3.7
# As of v1.1.0, child CA certificates generated by trustme fail 
# verification by CPython 3.13.
# https://github.com/python-trio/trustme/pull/642 
trustme @ git+https://github.com/python-trio/trustme@b3a767f336e20600f30c9ff78385a58352ff6ee3
cryptography==42.0.4
backports.zoneinfo==0.2.1;python_version<"3.9"
towncrier==23.6.0
pytest-memray==1.5.0;python_version<"3.13" and sys_platform!="win32" and implementation_name=="cpython"
trio==0.25.0
Quart==0.19.4
quart-trio==0.11.1
# https://github.com/pgjones/hypercorn/issues/62
# https://github.com/pgjones/hypercorn/issues/168
# https://github.com/pgjones/hypercorn/issues/169
hypercorn @ git+https://github.com/urllib3/hypercorn@urllib3-changes
httpx==0.25.2
pytest-socket==0.7.0
cffi==1.17.0rc1

