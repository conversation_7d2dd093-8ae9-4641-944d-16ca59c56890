{"kind": "discovery#restDescription", "discoveryVersion": "v1", "id": "bigqueryreservation:v1", "name": "bigqueryreservation", "canonicalName": "BigQuery Reservation", "version": "v1", "revision": "0", "title": "BigQuery Reservation API", "description": "A service to modify your BigQuery flat-rate reservations.", "ownerDomain": "google.com", "ownerName": "Google", "icons": {"x16": "http://www.google.com/images/icons/product/search-16.gif", "x32": "http://www.google.com/images/icons/product/search-32.gif"}, "documentationLink": "https://cloud.google.com/bigquery/", "protocol": "rest", "rootUrl": "https://bigqueryreservation.googleapis.com/", "mtlsRootUrl": "https://bigqueryreservation.mtls.googleapis.com/", "servicePath": "", "baseUrl": "https://bigqueryreservation.googleapis.com/", "batchPath": "batch", "version_module": true, "fullyEncodeReservedExpansion": true, "parameters": {"access_token": {"type": "string", "description": "OAuth access token.", "location": "query"}, "alt": {"type": "string", "description": "Data format for response.", "default": "json", "enum": ["json", "media", "proto"], "enumDescriptions": ["Responses with Content-Type of application/json", "Media download with context-dependent Content-Type", "Responses with Content-Type of application/x-protobuf"], "location": "query"}, "callback": {"type": "string", "description": "JSONP", "location": "query"}, "fields": {"type": "string", "description": "Selector specifying which fields to include in a partial response.", "location": "query"}, "key": {"type": "string", "description": "API key. Your API key identifies your project and provides you with API access, quota, and reports. Required unless you provide an OAuth 2.0 token.", "location": "query"}, "oauth_token": {"type": "string", "description": "OAuth 2.0 token for the current user.", "location": "query"}, "prettyPrint": {"type": "boolean", "description": "Returns response with indentations and line breaks.", "default": "true", "location": "query"}, "quotaUser": {"type": "string", "description": "Available to use for quota purposes for server-side applications. Can be any arbitrary string assigned to a user, but should not exceed 40 characters.", "location": "query"}, "upload_protocol": {"type": "string", "description": "Upload protocol for media (e.g. \"raw\", \"multipart\").", "location": "query"}, "uploadType": {"type": "string", "description": "Legacy upload protocol for media (e.g. \"media\", \"multipart\").", "location": "query"}, "$.xgafv": {"type": "string", "description": "V1 error format.", "enum": ["1", "2"], "enumDescriptions": ["v1 error format", "v2 error format"], "location": "query"}}, "auth": {"oauth2": {"scopes": {"https://www.googleapis.com/auth/bigquery": {"description": "View and manage your data in Google BigQuery and see the email address for your Google Account"}, "https://www.googleapis.com/auth/cloud-platform": {"description": "See, edit, configure, and delete your Google Cloud data and see the email address for your Google Account."}}}}, "schemas": {"Reservation": {"id": "Reservation", "description": "A reservation is a mechanism used to guarantee slots to users.", "type": "object", "properties": {"name": {"description": "Identifier. The resource name of the reservation, e.g., `projects/*/locations/*/reservations/team1-prod`. The reservation_id must only contain lower case alphanumeric characters or dashes. It must start with a letter and must not end with a dash. Its maximum length is 64 characters.", "type": "string"}, "slotCapacity": {"description": "Optional. Baseline slots available to this reservation. A slot is a unit of computational power in BigQuery, and serves as the unit of parallelism. Queries using this reservation might use more slots during runtime if ignore_idle_slots is set to false, or autoscaling is enabled. The total slot_capacity of the reservation and its siblings may exceed the total slot_count of capacity commitments. In that case, the exceeding slots will be charged with the autoscale SKU. You can increase the number of baseline slots in a reservation every few minutes. If you want to decrease your baseline slots, you are limited to once an hour if you have recently changed your baseline slot capacity and your baseline slots exceed your committed slots. Otherwise, you can decrease your baseline slots every few minutes.", "type": "string", "format": "int64"}, "ignoreIdleSlots": {"description": "Optional. If false, any query or pipeline job using this reservation will use idle slots from other reservations within the same admin project. If true, a query or pipeline job using this reservation will execute with the slot capacity specified in the slot_capacity field at most.", "type": "boolean"}, "isFrozen": {"description": "If true, disables self-management options for this reservation. Only internal users can use and override this setting.", "type": "boolean"}, "autoscale": {"description": "Optional. The configuration parameters for the auto scaling feature.", "$ref": "Autoscale"}, "idleSlotsPolicy": {"description": "Reservation policy for idle slots. When idle slots policy is set, ignore_idle_slots is set to the corresponding value: LOW and MEDIUM correspond to ignore_idle_slots=false, NONE to ignore_idle_slots=true. Note: this is an alpha feature.", "type": "string", "enumDescriptions": ["Idle slots policy is determined based on the ignore_idle_slots flag. If ignore_idle_slots=true, idle slots policy is set to NONE. If ignore_idle_slots=false, idle slots policy is set to MEDIUM.", "Projects running in this reservation do not consume idle slots.", "Projects running in this reservation consume idle slots at a low rate.", "Projects running in this reservation consume idle slots at a medium rate.", "Projects running in this reservation consume idle slots at a high rate."], "enum": ["IDLE_SLOTS_POLICY_UNSPECIFIED", "NONE", "LOW", "MEDIUM", "HIGH"]}, "maxConcurrency": {"description": "Maximum number of queries that are allowed to run concurrently in this reservation. Default value is 0 which means that maximum concurrency will be automatically set based on the reservation size. NOTE: this field is deprecated. Please use `concurrency` instead.", "deprecated": true, "type": "string", "format": "int64"}, "concurrency": {"description": "Optional. Job concurrency target which sets a soft upper bound on the number of jobs that can run concurrently in this reservation. This is a soft target due to asynchronous nature of the system and various optimizations for small queries. Default value is 0 which means that concurrency target will be automatically computed by the system. NOTE: this field is exposed as target job concurrency in the Information Schema, DDL and BigQuery CLI.", "type": "string", "format": "int64"}, "enableQueuingAndPriorities": {"description": "If true, enables reservation queuing and new job prioritization behavior for the reservation. Note: this feature is in preview.", "deprecated": true, "type": "boolean"}, "creationTime": {"description": "Output only. Creation time of the reservation.", "readOnly": true, "type": "string", "format": "google-datetime"}, "updateTime": {"description": "Output only. Last update time of the reservation.", "readOnly": true, "type": "string", "format": "google-datetime"}, "internalReservationId": {"description": "Output only.", "readOnly": true, "type": "string"}, "multiRegionAuxiliary": {"description": "Applicable only for reservations located within one of the BigQuery multi-regions (US or EU). If set to true, this reservation is placed in the organization's secondary region which is designated for disaster recovery purposes. If false, this reservation is placed in the organization's default region. NOTE: this is a preview feature. Project must be allow-listed in order to set this field.", "deprecated": true, "type": "boolean"}, "externallyUsedSlots": {"description": "Output only.", "readOnly": true, "type": "string", "format": "int64"}, "edition": {"description": "Optional. Edition of the reservation.", "type": "string", "enumDescriptions": ["Default value, which will be treated as ENTERPRISE.", "Standard edition.", "Basic edition.", "Enterprise edition.", "Enterprise Plus edition.", "Mission critical edition."], "enumDeprecated": [false, false, true, false, false, true], "enum": ["EDITION_UNSPECIFIED", "STANDARD", "BASIC", "ENTERPRISE", "ENTERPRISE_PLUS", "MISSION_CRITICAL"]}, "primaryLocation": {"description": "Output only. The current location of the reservation's primary replica. This field is only set for reservations using the managed disaster recovery feature.", "readOnly": true, "type": "string"}, "secondaryLocation": {"description": "Optional. The current location of the reservation's secondary replica. This field is only set for reservations using the managed disaster recovery feature. Users can set this in create reservation calls to create a failover reservation or in update reservation calls to convert a non-failover reservation to a failover reservation(or vice versa).", "type": "string"}, "originalPrimaryLocation": {"description": "Output only. The location where the reservation was originally created. This is set only during the failover reservation's creation. All billing charges for the failover reservation will be applied to this location.", "readOnly": true, "type": "string"}, "maxSlots": {"description": "Optional. The overall max slots for the reservation, covering slot_capacity (baseline), idle slots (if ignore_idle_slots is false) and scaled slots. If present, the reservation won't use more than the specified number of slots, even if there is demand and supply (from idle slots). NOTE: capping a reservation's idle slot usage is best effort and its usage may exceed the max_slots value. However, in terms of autoscale.current_slots (which accounts for the additional added slots), it will never exceed the max_slots - baseline. This field must be set together with the scaling_mode enum value, otherwise the request will be rejected with error code `google.rpc.Code.INVALID_ARGUMENT`. If the max_slots and scaling_mode are set, the autoscale or autoscale.max_slots field must be unset. Otherwise the request will be rejected with error code `google.rpc.Code.INVALID_ARGUMENT`. However, the autoscale field may still be in the output. The autopscale.max_slots will always show as 0 and the autoscaler.current_slots will represent the current slots from autoscaler excluding idle slots. For example, if the max_slots is 1000 and scaling_mode is AUTOSCALE_ONLY, then in the output, the autoscaler.max_slots will be 0 and the autoscaler.current_slots may be any value between 0 and 1000. If the max_slots is 1000, scaling_mode is ALL_SLOTS, the baseline is 100 and idle slots usage is 200, then in the output, the autoscaler.max_slots will be 0 and the autoscaler.current_slots will not be higher than 700. If the max_slots is 1000, scaling_mode is IDLE_SLOTS_ONLY, then in the output, the autoscaler field will be null. If the max_slots and scaling_mode are set, then the ignore_idle_slots field must be aligned with the scaling_mode enum value.(See details in ScalingMode comments). Otherwise the request will be rejected with error code `google.rpc.Code.INVALID_ARGUMENT`. Please note, the max_slots is for user to manage the part of slots greater than the baseline. Therefore, we don't allow users to set max_slots smaller or equal to the baseline as it will not be meaningful. If the field is present and slot_capacity>=max_slots, requests will be rejected with error code `google.rpc.Code.INVALID_ARGUMENT`. Please note that if max_slots is set to 0, we will treat it as unset. Customers can set max_slots to 0 and set scaling_mode to SCALING_MODE_UNSPECIFIED to disable the max_slots feature.", "type": "string", "format": "int64"}, "scalingMode": {"description": "Optional. The scaling mode for the reservation. If the field is present but max_slots is not present, requests will be rejected with error code `google.rpc.Code.INVALID_ARGUMENT`.", "type": "string", "enumDescriptions": ["Default value of ScalingMode.", "The reservation will scale up only using slots from autoscaling. It will not use any idle slots even if there may be some available. The upper limit that autoscaling can scale up to will be max_slots - baseline. For example, if max_slots is 1000, baseline is 200 and customer sets ScalingMode to AUTOSCALE_ONLY, then autoscalerg will scale up to 800 slots and no idle slots will be used. Please note, in this mode, the ignore_idle_slots field must be set to true. Otherwise the request will be rejected with error code `google.rpc.Code.INVALID_ARGUMENT`.", "The reservation will scale up using only idle slots contributed by other reservations or from unassigned commitments. If no idle slots are available it will not scale up further. If the idle slots which it is using are reclaimed by the contributing reservation(s) it may be forced to scale down. The max idle slots the reservation can be max_slots - baseline capacity. For example, if max_slots is 1000, baseline is 200 and customer sets ScalingMode to IDLE_SLOTS_ONLY, 1. if there are 1000 idle slots available in other reservations, the reservation will scale up to 1000 slots with 200 baseline and 800 idle slots. 2. if there are 500 idle slots available in other reservations, the reservation will scale up to 700 slots with 200 baseline and 300 idle slots. Please note, in this mode, the reservation might not be able to scale up to max_slots. Please note, in this mode, the ignore_idle_slots field must be set to false. Otherwise the request will be rejected with error code `google.rpc.Code.INVALID_ARGUMENT`.", "The reservation will scale up using all slots available to it. It will use idle slots contributed by other reservations or from unassigned commitments first. If no idle slots are available it will scale up using autoscaling. For example, if max_slots is 1000, baseline is 200 and customer sets ScalingMode to ALL_SLOTS, 1. if there are 800 idle slots available in other reservations, the reservation will scale up to 1000 slots with 200 baseline and 800 idle slots. 2. if there are 500 idle slots available in other reservations, the reservation will scale up to 1000 slots with 200 baseline, 500 idle slots and 300 autoscaling slots. 3. if there are no idle slots available in other reservations, it will scale up to 1000 slots with 200 baseline and 800 autoscaling slots. Please note, in this mode, the ignore_idle_slots field must be set to false. Otherwise the request will be rejected with error code `google.rpc.Code.INVALID_ARGUMENT`."], "enum": ["SCALING_MODE_UNSPECIFIED", "AUTOSCALE_ONLY", "IDLE_SLOTS_ONLY", "ALL_SLOTS"]}, "labels": {"description": "Optional. The labels associated with this reservation. You can use these to organize and group your reservations. You can set this property when inserting or updating a reservation.", "type": "object", "additionalProperties": {"type": "string"}}, "reservationGroup": {"description": "Optional. The reservation group this reservation belongs to. You can set this property when inserting or updating a reservation. Reservations do not need to belong to a reservation group. Format: projects/{project}/locations/{location}/reservationGroups/{reservation_group} or just {reservation_group}", "type": "string"}, "replicationStatus": {"description": "Output only. The Disaster Recovery(DR) replication status of the reservation. This is only available for the primary replicas of DR/failover reservations and provides information about the both the staleness of the secondary and the last error encountered while trying to replicate changes from the primary to the secondary. If this field is blank, it means that the reservation is either not a DR reservation or the reservation is a DR secondary or that any replication operations on the reservation have succeeded.", "readOnly": true, "$ref": "ReplicationStatus"}, "projectScheduling": {"description": "Optional. Distribute resources fairly across projects, possibly subject to per-project caps.", "$ref": "ProjectScheduling"}}}, "Autoscale": {"id": "Autoscale", "description": "Auto scaling settings.", "type": "object", "properties": {"currentSlots": {"description": "Output only. The slot capacity added to this reservation when autoscale happens. Will be between [0, max_slots]. Note: after users reduce max_slots, it may take a while before it can be propagated, so current_slots may stay in the original value and could be larger than max_slots for that brief period (less than one minute)", "readOnly": true, "type": "string", "format": "int64"}, "maxSlots": {"description": "Optional. Number of slots to be scaled when needed.", "type": "string", "format": "int64"}, "currentUsedSlots": {"description": "Output only. The number of slots currently used by On Demand Autoscaler", "readOnly": true, "type": "string", "format": "int64"}}}, "ReplicationStatus": {"id": "ReplicationStatus", "description": "Disaster Recovery(DR) replication status of the reservation.", "type": "object", "properties": {"error": {"description": "Output only. The last error encountered while trying to replicate changes from the primary to the secondary. This field is only available if the replication has not succeeded since.", "readOnly": true, "$ref": "Status"}, "lastErrorTime": {"description": "Output only. The time at which the last error was encountered while trying to replicate changes from the primary to the secondary. This field is only available if the replication has not succeeded since.", "readOnly": true, "type": "string", "format": "google-datetime"}, "lastReplicationTime": {"description": "Output only. A timestamp corresponding to the last change on the primary that was successfully replicated to the secondary.", "readOnly": true, "type": "string", "format": "google-datetime"}, "softFailoverStartTime": {"description": "Output only. The time at which a soft failover for the reservation and its associated datasets was initiated. After this field is set, all subsequent changes to the reservation will be rejected unless a hard failover overrides this operation. This field will be cleared once the failover is complete.", "readOnly": true, "type": "string", "format": "google-datetime"}}}, "Status": {"id": "Status", "description": "The `Status` type defines a logical error model that is suitable for different programming environments, including REST APIs and RPC APIs. It is used by [gRPC](https://github.com/grpc). Each `Status` message contains three pieces of data: error code, error message, and error details. You can find out more about this error model and how to work with it in the [API Design Guide](https://cloud.google.com/apis/design/errors).", "type": "object", "properties": {"code": {"description": "The status code, which should be an enum value of google.rpc.Code.", "type": "integer", "format": "int32"}, "message": {"description": "A developer-facing error message, which should be in English. Any user-facing error message should be localized and sent in the google.rpc.Status.details field, or localized by the client.", "type": "string"}, "details": {"description": "A list of messages that carry the error details. There is a common set of message types for APIs to use.", "type": "array", "items": {"type": "object", "additionalProperties": {"type": "any", "description": "Properties of the object. Contains field @type with type URL."}}}}}, "ProjectScheduling": {"id": "ProjectScheduling", "description": "Configures project fair scheduling within the reservation.", "type": "object", "properties": {"concurrency": {"description": "Optional. If present, the reservation will attempt to limit the per-project concurrency of jobs running under the reservation to the specified number of jobs.", "type": "string", "format": "int64"}, "maxSlots": {"description": "Optional. If present, the reservation will attempt to limit the average slot usage of any project running within it to the specified rate.", "type": "string", "format": "int64"}}}, "ListReservationsResponse": {"id": "ListReservationsResponse", "description": "The response for ReservationService.ListReservations.", "type": "object", "properties": {"reservations": {"description": "List of reservations visible to the user.", "type": "array", "items": {"$ref": "Reservation"}}, "nextPageToken": {"description": "Token to retrieve the next page of results, or empty if there are no more results in the list.", "type": "string"}}}, "Empty": {"id": "Empty", "description": "A generic empty message that you can re-use to avoid defining duplicated empty messages in your APIs. A typical example is to use it as the request or the response type of an API method. For instance: service Foo { rpc Bar(google.protobuf.Empty) returns (google.protobuf.Empty); }", "type": "object", "properties": {}}, "FailoverReservationRequest": {"id": "FailoverReservationRequest", "description": "The request for ReservationService.FailoverReservation.", "type": "object", "properties": {"failoverMode": {"description": "Optional. failover mode for the failover operation.", "type": "string", "enumDescriptions": ["Invalid value.", "When customers initiate a soft failover, BigQuery will wait until all committed writes are replicated to the secondary.", "When customers initiate a hard failover, BigQuery will not wait until all committed writes are replicated to the secondary. There can be data loss for hard failover."], "enum": ["FAILOVER_MODE_UNSPECIFIED", "SOFT", "HARD"]}, "force": {"description": "Optional. True if the request is forcing the current location to be the primary location. Only oncall should use it. By forcing the primary location, we can mitigate the issue as we have 2 active primaries when the original primary is available again.", "type": "boolean"}}}, "CapacityCommitment": {"id": "CapacityCommitment", "description": "Capacity commitment is a way to purchase compute capacity for BigQuery jobs (in the form of slots) with some committed period of usage. Annual commitments renew by default. Commitments can be removed after their commitment end time passes. In order to remove annual commitment, its plan needs to be changed to monthly or flex first. A capacity commitment resource exists as a child resource of the admin project.", "type": "object", "properties": {"name": {"description": "Output only. The resource name of the capacity commitment, e.g., `projects/myproject/locations/US/capacityCommitments/123` The commitment_id must only contain lower case alphanumeric characters or dashes. It must start with a letter and must not end with a dash. Its maximum length is 64 characters.", "readOnly": true, "type": "string"}, "slotCount": {"description": "Optional. Number of slots in this commitment.", "type": "string", "format": "int64"}, "plan": {"description": "Optional. Capacity commitment commitment plan.", "type": "string", "enumDescriptions": ["Invalid plan value. Requests with this value will be rejected with error code `google.rpc.Code.INVALID_ARGUMENT`.", "Capacity commitment is free of charge and can be removed at any point.", "Flex commitments have committed period of 1 minute after becoming ACTIVE. After that, they are not in a committed period anymore and can be removed any time.", "Same as FLEX, should only be used if flat-rate commitments are still available.", "Alias as FLEX_FLAT_RATE", "Trial commitments have a committed period of 182 days after becoming ACTIVE. After that, they are converted to a new commitment based on the `renewal_plan`. Default `renewal_plan` for Trial commitment is Flex so that it can be deleted right after committed period ends.", "Monthly commitments have a committed period of 30 days after becoming ACTIVE. After that, they are not in a committed period anymore and can be removed any time.", "Same as MONTHLY, should only be used if flat-rate commitments are still available.", "<PERSON><PERSON> as MONTHLY_FLAT_RATE", "Annual commitments have a committed period of 365 days after becoming ACTIVE. After that they are converted to a new commitment based on the renewal_plan.", "Same as ANNUAL, should only be used if flat-rate commitments are still available.", "<PERSON><PERSON> as ANNUAL_FLAT_RATE", "3-year commitments have a committed period of 1095(3 * 365) days after becoming ACTIVE. After that they are converted to a new commitment based on the renewal_plan.", "Should only be used for `renewal_plan` and is only meaningful if edition is specified to values other than EDITION_UNSPECIFIED. Otherwise CreateCapacityCommitmentRequest or UpdateCapacityCommitmentRequest will be rejected with error code `google.rpc.Code.INVALID_ARGUMENT`. If the renewal_plan is NONE, capacity commitment will be removed at the end of its commitment period."], "enumDeprecated": [false, false, false, true, true, true, false, true, true, false, true, true, false, false], "enum": ["COMMITMENT_PLAN_UNSPECIFIED", "FREE", "FLEX", "FLEX_FLAT_RATE", "FLEX_LEGACY", "TRIAL", "MONTHLY", "MONTHLY_FLAT_RATE", "MONTHLY_LEGACY", "ANNUAL", "ANNUAL_FLAT_RATE", "ANNUAL_LEGACY", "THREE_YEAR", "NONE"]}, "state": {"description": "Output only. State of the commitment.", "readOnly": true, "type": "string", "enumDescriptions": ["Invalid state value.", "Capacity commitment is pending provisioning. Pending capacity commitment does not contribute to the project's slot_capacity.", "Once slots are provisioned, capacity commitment becomes active. slot_count is added to the project's slot_capacity.", "Capacity commitment is failed to be activated by the backend."], "enum": ["STATE_UNSPECIFIED", "PENDING", "ACTIVE", "FAILED"]}, "commitmentStartTime": {"description": "Output only. The start of the current commitment period. It is applicable only for ACTIVE capacity commitments. Note after the commitment is renewed, commitment_start_time won't be changed. It refers to the start time of the original commitment.", "readOnly": true, "type": "string", "format": "google-datetime"}, "commitmentEndTime": {"description": "Output only. The end of the current commitment period. It is applicable only for ACTIVE capacity commitments. Note after renewal, commitment_end_time is the time the renewed commitment expires. So itwould be at a time after commitment_start_time + committed period, because we don't change commitment_start_time ,", "readOnly": true, "type": "string", "format": "google-datetime"}, "isFrozen": {"description": "If true, disables self-management options for this reservation. Only internal users can use and override this setting.", "type": "boolean"}, "failureStatus": {"description": "Output only. For FAILED commitment plan, provides the reason of failure.", "readOnly": true, "$ref": "Status"}, "renewalPlan": {"description": "Optional. The plan this capacity commitment is converted to after commitment_end_time passes. Once the plan is changed, committed period is extended according to commitment plan. Only applicable for ANNUAL and TRIAL commitments.", "type": "string", "enumDescriptions": ["Invalid plan value. Requests with this value will be rejected with error code `google.rpc.Code.INVALID_ARGUMENT`.", "Capacity commitment is free of charge and can be removed at any point.", "Flex commitments have committed period of 1 minute after becoming ACTIVE. After that, they are not in a committed period anymore and can be removed any time.", "Same as FLEX, should only be used if flat-rate commitments are still available.", "Alias as FLEX_FLAT_RATE", "Trial commitments have a committed period of 182 days after becoming ACTIVE. After that, they are converted to a new commitment based on the `renewal_plan`. Default `renewal_plan` for Trial commitment is Flex so that it can be deleted right after committed period ends.", "Monthly commitments have a committed period of 30 days after becoming ACTIVE. After that, they are not in a committed period anymore and can be removed any time.", "Same as MONTHLY, should only be used if flat-rate commitments are still available.", "<PERSON><PERSON> as MONTHLY_FLAT_RATE", "Annual commitments have a committed period of 365 days after becoming ACTIVE. After that they are converted to a new commitment based on the renewal_plan.", "Same as ANNUAL, should only be used if flat-rate commitments are still available.", "<PERSON><PERSON> as ANNUAL_FLAT_RATE", "3-year commitments have a committed period of 1095(3 * 365) days after becoming ACTIVE. After that they are converted to a new commitment based on the renewal_plan.", "Should only be used for `renewal_plan` and is only meaningful if edition is specified to values other than EDITION_UNSPECIFIED. Otherwise CreateCapacityCommitmentRequest or UpdateCapacityCommitmentRequest will be rejected with error code `google.rpc.Code.INVALID_ARGUMENT`. If the renewal_plan is NONE, capacity commitment will be removed at the end of its commitment period."], "enumDeprecated": [false, false, false, true, true, true, false, true, true, false, true, true, false, false], "enum": ["COMMITMENT_PLAN_UNSPECIFIED", "FREE", "FLEX", "FLEX_FLAT_RATE", "FLEX_LEGACY", "TRIAL", "MONTHLY", "MONTHLY_FLAT_RATE", "MONTHLY_LEGACY", "ANNUAL", "ANNUAL_FLAT_RATE", "ANNUAL_LEGACY", "THREE_YEAR", "NONE"]}, "multiRegionAuxiliary": {"description": "Applicable only for commitments located within one of the BigQuery multi-regions (US or EU). If set to true, this commitment is placed in the organization's secondary region which is designated for disaster recovery purposes. If false, this commitment is placed in the organization's default region. NOTE: this is a preview feature. Project must be allow-listed in order to set this field.", "deprecated": true, "type": "boolean"}, "edition": {"description": "Optional. Edition of the capacity commitment.", "type": "string", "enumDescriptions": ["Default value, which will be treated as ENTERPRISE.", "Standard edition.", "Basic edition.", "Enterprise edition.", "Enterprise Plus edition.", "Mission critical edition."], "enumDeprecated": [false, false, true, false, false, true], "enum": ["EDITION_UNSPECIFIED", "STANDARD", "BASIC", "ENTERPRISE", "ENTERPRISE_PLUS", "MISSION_CRITICAL"]}, "neededAutoscaleSlotCount": {"description": "The number of autoscale slots initially requested (needed), but could be subject to squeezing. This field was added for the convenience of coding, but shouldn't be exposed to any customers.", "type": "string", "format": "int64"}, "isFlatRate": {"description": "Output only. If true, the commitment is a flat-rate commitment, otherwise, it's an edition commitment.", "readOnly": true, "type": "boolean"}}}, "ListCapacityCommitmentsResponse": {"id": "ListCapacityCommitmentsResponse", "description": "The response for ReservationService.ListCapacityCommitments.", "type": "object", "properties": {"capacityCommitments": {"description": "List of capacity commitments visible to the user.", "type": "array", "items": {"$ref": "CapacityCommitment"}}, "nextPageToken": {"description": "Token to retrieve the next page of results, or empty if there are no more results in the list.", "type": "string"}}}, "SplitCapacityCommitmentRequest": {"id": "SplitCapacityCommitmentRequest", "description": "The request for ReservationService.SplitCapacityCommitment.", "type": "object", "properties": {"slotCount": {"description": "Number of slots in the capacity commitment after the split.", "type": "string", "format": "int64"}}}, "SplitCapacityCommitmentResponse": {"id": "SplitCapacityCommitmentResponse", "description": "The response for ReservationService.SplitCapacityCommitment.", "type": "object", "properties": {"first": {"description": "First capacity commitment, result of a split.", "$ref": "CapacityCommitment"}, "second": {"description": "Second capacity commitment, result of a split.", "$ref": "CapacityCommitment"}}}, "MergeCapacityCommitmentsRequest": {"id": "MergeCapacityCommitmentsRequest", "description": "The request for ReservationService.MergeCapacityCommitments.", "type": "object", "properties": {"capacityCommitmentIds": {"description": "Ids of capacity commitments to merge. These capacity commitments must exist under admin project and location specified in the parent. ID is the last portion of capacity commitment name e.g., 'abc' for projects/myproject/locations/US/capacityCommitments/abc", "type": "array", "items": {"type": "string"}}, "capacityCommitmentId": {"description": "Optional. The optional resulting capacity commitment ID. Capacity commitment name will be generated automatically if this field is empty. This field must only contain lower case alphanumeric characters or dashes. The first and last character cannot be a dash. Max length is 64 characters.", "type": "string"}}}, "Assignment": {"id": "Assignment", "description": "An assignment allows a project to submit jobs of a certain type using slots from the specified reservation.", "type": "object", "properties": {"name": {"description": "Output only. Name of the resource. E.g.: `projects/myproject/locations/US/reservations/team1-prod/assignments/123`. The assignment_id must only contain lower case alphanumeric characters or dashes and the max length is 64 characters.", "readOnly": true, "type": "string"}, "assignee": {"description": "Optional. The resource which will use the reservation. E.g. `projects/myproject`, `folders/123`, or `organizations/456`.", "type": "string"}, "jobType": {"description": "Optional. Which type of jobs will use the reservation.", "type": "string", "enumDescriptions": ["Invalid type. Requests with this value will be rejected with error code `google.rpc.Code.INVALID_ARGUMENT`.", "Pipeline (load/export) jobs from the project will use the reservation.", "Query jobs from the project will use the reservation.", "BigQuery ML jobs that use services external to BigQuery for model training. These jobs will not utilize idle slots from other reservations.", "Background jobs that Big<PERSON><PERSON><PERSON> runs for the customers in the background.", "BigQuery CALL SPARK PROCEDURE jobs that use an external services for running PySpark code. These jobs will not utilize idle slots from other reservations.", "Continuous SQL jobs will use this reservation. Reservations with continuous assignments cannot be mixed with non-continuous assignments."], "enum": ["JOB_TYPE_UNSPECIFIED", "PIPELINE", "QUERY", "ML_EXTERNAL", "BACKGROUND", "SPARK", "CONTINUOUS"]}, "state": {"description": "Output only. State of the assignment.", "readOnly": true, "type": "string", "enumDescriptions": ["Invalid state value.", "Queries from assignee will be executed as on-demand, if related assignment is pending.", "Assignment is ready."], "enum": ["STATE_UNSPECIFIED", "PENDING", "ACTIVE"]}, "priority": {"description": "Default priority of the queries that are using this assignment. It only applies to the assignments of job_type 'QUERY'.", "type": "string", "enumDescriptions": ["Invalid priority.", "Not a latency-sensitive query. With large enough slot demand, batch query will get 2x less resources than an interactive query in the same project.", "Latency-sensitive query.", "Highly latency-sensitive query. With large enough slot demand, high priority query will get 2x more resources than an interactive query in the same project."], "enum": ["JOB_PRIORITY_UNSPECIFIED", "BATCH", "INTERACTIVE", "HIGH"]}, "creationTime": {"description": "Output only. Creation time of the assignment.", "readOnly": true, "type": "string", "format": "google-datetime"}, "updateTime": {"description": "Output only. Last update time of the assignment. If the assignment never got modified after creation, this will be the same as creation_time.", "readOnly": true, "type": "string", "format": "google-datetime"}, "enableGeminiInBigquery": {"description": "Optional. This field controls if \"Gemini in BigQuery\" (https://cloud.google.com/gemini/docs/bigquery/overview) features should be enabled for this reservation assignment, which is not on by default. \"Gemini in BigQuery\" has a distinct compliance posture from BigQuery. If this field is set to true, the assignment job type is QUERY, and the parent reservation edition is ENTERPRISE_PLUS, then the assignment will give the grantee project/organization access to \"Gemini in BigQuery\" features.", "type": "boolean"}}}, "ListAssignmentsResponse": {"id": "ListAssignmentsResponse", "description": "The response for ReservationService.ListAssignments.", "type": "object", "properties": {"assignments": {"description": "List of assignments visible to the user.", "type": "array", "items": {"$ref": "Assignment"}}, "nextPageToken": {"description": "Token to retrieve the next page of results, or empty if there are no more results in the list.", "type": "string"}}}, "SearchAssignmentsResponse": {"id": "SearchAssignmentsResponse", "description": "The response for ReservationService.SearchAssignments.", "type": "object", "properties": {"assignments": {"description": "List of assignments visible to the user.", "type": "array", "items": {"$ref": "Assignment"}}, "nextPageToken": {"description": "Token to retrieve the next page of results, or empty if there are no more results in the list.", "type": "string"}}}, "SearchAllAssignmentsResponse": {"id": "SearchAllAssignmentsResponse", "description": "The response for ReservationService.SearchAllAssignments.", "type": "object", "properties": {"assignments": {"description": "List of assignments visible to the user.", "type": "array", "items": {"$ref": "Assignment"}}, "nextPageToken": {"description": "Token to retrieve the next page of results, or empty if there are no more results in the list.", "type": "string"}}}, "MoveAssignmentRequest": {"id": "MoveAssignmentRequest", "description": "The request for ReservationService.MoveAssignment. **Note**: \"bigquery.reservationAssignments.create\" permission is required on the destination_id. **Note**: \"bigquery.reservationAssignments.create\" and \"bigquery.reservationAssignments.delete\" permission are required on the related assignee.", "type": "object", "properties": {"destinationId": {"description": "The new reservation ID, e.g.: `projects/myotherproject/locations/US/reservations/team2-prod`", "type": "string"}, "assignmentId": {"description": "The optional assignment ID. A new assignment name is generated if this field is empty. This field can contain only lowercase alphanumeric characters or dashes. Max length is 64 characters.", "type": "string"}, "useFrozenReservations": {"description": "If true, allow use of frozen reservations for this action. This is an internal-only field intended to migrate existing reservations on to reservation service.", "type": "boolean"}}}, "BiReservation": {"id": "BiReservation", "description": "Represents a BI Reservation.", "type": "object", "properties": {"name": {"description": "Identifier. The resource name of the singleton BI reservation. Reservation names have the form `projects/{project_id}/locations/{location_id}/biReservation`.", "type": "string"}, "updateTime": {"description": "Output only. The last update timestamp of a reservation.", "readOnly": true, "type": "string", "format": "google-datetime"}, "size": {"description": "Optional. Size of a reservation, in bytes.", "type": "string", "format": "int64"}, "preferredTables": {"description": "Optional. Preferred tables to use BI capacity for.", "type": "array", "items": {"$ref": "TableReference"}}}}, "TableReference": {"id": "TableReference", "description": "Fully qualified reference to BigQuery table. Internally stored as google.cloud.bi.v1.BqTableReference.", "type": "object", "properties": {"projectId": {"description": "Optional. The assigned project ID of the project.", "type": "string"}, "datasetId": {"description": "Optional. The ID of the dataset in the above project.", "type": "string"}, "tableId": {"description": "Optional. The ID of the table in the above dataset.", "type": "string"}}}, "UpgradeEditionRequest": {"id": "UpgradeEditionRequest", "description": "The request of UpgradeEdition. At least one reservation or capacity commitment should be specified.", "type": "object", "properties": {"capacityCommitmentNames": {"description": "Resource names of the capacity commitments to upgrade. These capacity commitments must exist under the specified parent. Commitment start/end time won’t be changed after updating.", "type": "array", "items": {"type": "string"}}, "reservationNames": {"description": "Resource names of the reservations to upgrade. These reservations must exist under the specified parent.", "type": "array", "items": {"type": "string"}}, "targetEdition": {"description": "The edition to upgrade to.", "type": "string", "enumDescriptions": ["Default value, which will be treated as ENTERPRISE.", "Standard edition.", "Basic edition.", "Enterprise edition.", "Enterprise Plus edition.", "Mission critical edition."], "enumDeprecated": [false, false, true, false, false, true], "enum": ["EDITION_UNSPECIFIED", "STANDARD", "BASIC", "ENTERPRISE", "ENTERPRISE_PLUS", "MISSION_CRITICAL"]}}}, "UpgradeEditionResponse": {"id": "UpgradeEditionResponse", "description": "The response of UpgradeEdition.", "type": "object", "properties": {"capacityCommitments": {"description": "Impacted capacity commitments after the upgrade.", "type": "array", "items": {"$ref": "CapacityCommitment"}}, "reservations": {"description": "Impacted reservations after the upgrade.", "type": "array", "items": {"$ref": "Reservation"}}}}, "Policy": {"id": "Policy", "description": "An Identity and Access Management (IAM) policy, which specifies access controls for Google Cloud resources. A `Policy` is a collection of `bindings`. A `binding` binds one or more `members`, or principals, to a single `role`. Principals can be user accounts, service accounts, Google groups, and domains (such as G Suite). A `role` is a named list of permissions; each `role` can be an IAM predefined role or a user-created custom role. For some types of Google Cloud resources, a `binding` can also specify a `condition`, which is a logical expression that allows access to a resource only if the expression evaluates to `true`. A condition can add constraints based on attributes of the request, the resource, or both. To learn which resources support conditions in their IAM policies, see the [IAM documentation](https://cloud.google.com/iam/help/conditions/resource-policies). **JSON example:** ``` { \"bindings\": [ { \"role\": \"roles/resourcemanager.organizationAdmin\", \"members\": [ \"user:<EMAIL>\", \"group:<EMAIL>\", \"domain:google.com\", \"serviceAccount:<EMAIL>\" ] }, { \"role\": \"roles/resourcemanager.organizationViewer\", \"members\": [ \"user:<EMAIL>\" ], \"condition\": { \"title\": \"expirable access\", \"description\": \"Does not grant access after Sep 2020\", \"expression\": \"request.time < timestamp('2020-10-01T00:00:00.000Z')\", } } ], \"etag\": \"BwWWja0YfJA=\", \"version\": 3 } ``` **YAML example:** ``` bindings: - members: - user:<EMAIL> - group:<EMAIL> - domain:google.com - serviceAccount:<EMAIL> role: roles/resourcemanager.organizationAdmin - members: - user:<EMAIL> role: roles/resourcemanager.organizationViewer condition: title: expirable access description: Does not grant access after Sep 2020 expression: request.time < timestamp('2020-10-01T00:00:00.000Z') etag: BwWWja0YfJA= version: 3 ``` For a description of IAM and its features, see the [IAM documentation](https://cloud.google.com/iam/docs/).", "type": "object", "properties": {"version": {"description": "Specifies the format of the policy. Valid values are `0`, `1`, and `3`. Requests that specify an invalid value are rejected. Any operation that affects conditional role bindings must specify version `3`. This requirement applies to the following operations: * Getting a policy that includes a conditional role binding * Adding a conditional role binding to a policy * Changing a conditional role binding in a policy * Removing any role binding, with or without a condition, from a policy that includes conditions **Important:** If you use IAM Conditions, you must include the `etag` field whenever you call `setIamPolicy`. If you omit this field, then IAM allows you to overwrite a version `3` policy with a version `1` policy, and all of the conditions in the version `3` policy are lost. If a policy does not include any conditions, operations on that policy may specify any valid version or leave the field unset. To learn which resources support conditions in their IAM policies, see the [IAM documentation](https://cloud.google.com/iam/help/conditions/resource-policies).", "type": "integer", "format": "int32"}, "bindings": {"description": "Associates a list of `members`, or principals, with a `role`. Optionally, may specify a `condition` that determines how and when the `bindings` are applied. Each of the `bindings` must contain at least one principal. The `bindings` in a `Policy` can refer to up to 1,500 principals; up to 250 of these principals can be Google groups. Each occurrence of a principal counts towards these limits. For example, if the `bindings` grant 50 different roles to `user:<EMAIL>`, and not to any other principal, then you can add another 1,450 principals to the `bindings` in the `Policy`.", "type": "array", "items": {"$ref": "Binding"}}, "auditConfigs": {"description": "Specifies cloud audit logging configuration for this policy.", "type": "array", "items": {"$ref": "AuditConfig"}}, "rules": {"description": "If more than one rule is specified, the rules are applied in the following manner: - All matching LOG rules are always applied. - If any DENY/DENY_WITH_LOG rule matches, permission is denied. Logging will be applied if one or more matching rule requires logging. - Otherwise, if any ALLOW/ALLOW_WITH_LOG rule matches, permission is granted. Logging will be applied if one or more matching rule requires logging. - Otherwise, if no rule applies, permission is denied.", "type": "array", "items": {"$ref": "Rule"}}, "etag": {"description": "`etag` is used for optimistic concurrency control as a way to help prevent simultaneous updates of a policy from overwriting each other. It is strongly suggested that systems make use of the `etag` in the read-modify-write cycle to perform policy updates in order to avoid race conditions: An `etag` is returned in the response to `getIamPolicy`, and systems are expected to put that etag in the request to `setIamPolicy` to ensure that their change will be applied to the same version of the policy. **Important:** If you use IAM Conditions, you must include the `etag` field whenever you call `setIamPolicy`. If you omit this field, then IAM allows you to overwrite a version `3` policy with a version `1` policy, and all of the conditions in the version `3` policy are lost.", "type": "string", "format": "byte"}}}, "Binding": {"id": "Binding", "description": "Associates `members`, or principals, with a `role`.", "type": "object", "properties": {"role": {"description": "Role that is assigned to the list of `members`, or principals. For example, `roles/viewer`, `roles/editor`, or `roles/owner`. For an overview of the IAM roles and permissions, see the [IAM documentation](https://cloud.google.com/iam/docs/roles-overview). For a list of the available pre-defined roles, see [here](https://cloud.google.com/iam/docs/understanding-roles).", "type": "string"}, "members": {"description": "Specifies the principals requesting access for a Google Cloud resource. `members` can have the following values: * `allUsers`: A special identifier that represents anyone who is on the internet; with or without a Google account. * `allAuthenticatedUsers`: A special identifier that represents anyone who is authenticated with a Google account or a service account. Does not include identities that come from external identity providers (IdPs) through identity federation. * `user:{emailid}`: An email address that represents a specific Google account. For example, `<EMAIL>` . * `serviceAccount:{emailid}`: An email address that represents a Google service account. For example, `<EMAIL>`. * `serviceAccount:{projectid}.svc.id.goog[{namespace}/{kubernetes-sa}]`: An identifier for a [Kubernetes service account](https://cloud.google.com/kubernetes-engine/docs/how-to/kubernetes-service-accounts). For example, `my-project.svc.id.goog[my-namespace/my-kubernetes-sa]`. * `group:{emailid}`: An email address that represents a Google group. For example, `<EMAIL>`. * `domain:{domain}`: The G Suite domain (primary) that represents all the users of that domain. For example, `google.com` or `example.com`. * `principal://iam.googleapis.com/locations/global/workforcePools/{pool_id}/subject/{subject_attribute_value}`: A single identity in a workforce identity pool. * `principalSet://iam.googleapis.com/locations/global/workforcePools/{pool_id}/group/{group_id}`: All workforce identities in a group. * `principalSet://iam.googleapis.com/locations/global/workforcePools/{pool_id}/attribute.{attribute_name}/{attribute_value}`: All workforce identities with a specific attribute value. * `principalSet://iam.googleapis.com/locations/global/workforcePools/{pool_id}/*`: All identities in a workforce identity pool. * `principal://iam.googleapis.com/projects/{project_number}/locations/global/workloadIdentityPools/{pool_id}/subject/{subject_attribute_value}`: A single identity in a workload identity pool. * `principalSet://iam.googleapis.com/projects/{project_number}/locations/global/workloadIdentityPools/{pool_id}/group/{group_id}`: A workload identity pool group. * `principalSet://iam.googleapis.com/projects/{project_number}/locations/global/workloadIdentityPools/{pool_id}/attribute.{attribute_name}/{attribute_value}`: All identities in a workload identity pool with a certain attribute. * `principalSet://iam.googleapis.com/projects/{project_number}/locations/global/workloadIdentityPools/{pool_id}/*`: All identities in a workload identity pool. * `deleted:user:{emailid}?uid={uniqueid}`: An email address (plus unique identifier) representing a user that has been recently deleted. For example, `<EMAIL>?uid=123456789012345678901`. If the user is recovered, this value reverts to `user:{emailid}` and the recovered user retains the role in the binding. * `deleted:serviceAccount:{emailid}?uid={uniqueid}`: An email address (plus unique identifier) representing a service account that has been recently deleted. For example, `<EMAIL>?uid=123456789012345678901`. If the service account is undeleted, this value reverts to `serviceAccount:{emailid}` and the undeleted service account retains the role in the binding. * `deleted:group:{emailid}?uid={uniqueid}`: An email address (plus unique identifier) representing a Google group that has been recently deleted. For example, `<EMAIL>?uid=123456789012345678901`. If the group is recovered, this value reverts to `group:{emailid}` and the recovered group retains the role in the binding. * `deleted:principal://iam.googleapis.com/locations/global/workforcePools/{pool_id}/subject/{subject_attribute_value}`: Deleted single identity in a workforce identity pool. For example, `deleted:principal://iam.googleapis.com/locations/global/workforcePools/my-pool-id/subject/my-subject-attribute-value`.", "type": "array", "items": {"type": "string"}}, "condition": {"description": "The condition that is associated with this binding. If the condition evaluates to `true`, then this binding applies to the current request. If the condition evaluates to `false`, then this binding does not apply to the current request. However, a different role binding might grant the same role to one or more of the principals in this binding. To learn which resources support conditions in their IAM policies, see the [IAM documentation](https://cloud.google.com/iam/help/conditions/resource-policies).", "$ref": "Expr"}, "bindingId": {"type": "string"}}}, "Expr": {"id": "Expr", "description": "Represents a textual expression in the Common Expression Language (CEL) syntax. CEL is a C-like expression language. The syntax and semantics of CEL are documented at https://github.com/google/cel-spec. Example (Comparison): title: \"Summary size limit\" description: \"Determines if a summary is less than 100 chars\" expression: \"document.summary.size() < 100\" Example (Equality): title: \"Requestor is owner\" description: \"Determines if requestor is the document owner\" expression: \"document.owner == request.auth.claims.email\" Example (Logic): title: \"Public documents\" description: \"Determine whether the document should be publicly visible\" expression: \"document.type != 'private' && document.type != 'internal'\" Example (Data Manipulation): title: \"Notification string\" description: \"Create a notification string with a timestamp.\" expression: \"'New message received at ' + string(document.create_time)\" The exact variables and functions that may be referenced within an expression are determined by the service that evaluates it. See the service documentation for additional information.", "type": "object", "properties": {"expression": {"description": "Textual representation of an expression in Common Expression Language syntax.", "type": "string"}, "title": {"description": "Optional. Title for the expression, i.e. a short string describing its purpose. This can be used e.g. in UIs which allow to enter the expression.", "type": "string"}, "description": {"description": "Optional. Description of the expression. This is a longer text which describes the expression, e.g. when hovered over it in a UI.", "type": "string"}, "location": {"description": "Optional. String indicating the location of the expression for error reporting, e.g. a file name and a position in the file.", "type": "string"}}}, "AuditConfig": {"id": "AuditConfig", "description": "Specifies the audit configuration for a service. The configuration determines which permission types are logged, and what identities, if any, are exempted from logging. An AuditConfig must have one or more AuditLogConfigs. If there are AuditConfigs for both `allServices` and a specific service, the union of the two AuditConfigs is used for that service: the log_types specified in each AuditConfig are enabled, and the exempted_members in each AuditLogConfig are exempted. Example Policy with multiple AuditConfigs: { \"audit_configs\": [ { \"service\": \"allServices\", \"audit_log_configs\": [ { \"log_type\": \"DATA_READ\", \"exempted_members\": [ \"user:<EMAIL>\" ] }, { \"log_type\": \"DATA_WRITE\" }, { \"log_type\": \"ADMIN_READ\" } ] }, { \"service\": \"sampleservice.googleapis.com\", \"audit_log_configs\": [ { \"log_type\": \"DATA_READ\" }, { \"log_type\": \"DATA_WRITE\", \"exempted_members\": [ \"user:<EMAIL>\" ] } ] } ] } For sampleservice, this policy enables DATA_READ, DATA_WRITE and ADMIN_READ logging. It also exempts `<EMAIL>` from DATA_READ logging, and `<EMAIL>` from DATA_WRITE logging.", "type": "object", "properties": {"service": {"description": "Specifies a service that will be enabled for audit logging. For example, `storage.googleapis.com`, `cloudsql.googleapis.com`. `allServices` is a special value that covers all services.", "type": "string"}, "auditLogConfigs": {"description": "The configuration for logging of each type of permission.", "type": "array", "items": {"$ref": "AuditLogConfig"}}}}, "AuditLogConfig": {"id": "AuditLogConfig", "description": "Provides the configuration for logging a type of permissions. Example: { \"audit_log_configs\": [ { \"log_type\": \"DATA_READ\", \"exempted_members\": [ \"user:<EMAIL>\" ] }, { \"log_type\": \"DATA_WRITE\" } ] } This enables 'DATA_READ' and 'DATA_WRITE' logging, <NAME_EMAIL> from DATA_READ logging.", "type": "object", "properties": {"logType": {"description": "The log type that this config enables.", "type": "string", "enumDescriptions": ["Default case. Should never be this.", "Admin reads. Example: CloudIAM getIamPolicy", "Data writes. Example: CloudSQL Users create", "Data reads. Example: CloudSQL Users list"], "enum": ["LOG_TYPE_UNSPECIFIED", "ADMIN_READ", "DATA_WRITE", "DATA_READ"]}, "exemptedMembers": {"description": "Specifies the identities that do not cause logging for this type of permission. Follows the same format of Binding.members.", "type": "array", "items": {"type": "string"}}, "ignoreChildExemptions": {"type": "boolean"}}}, "Rule": {"id": "Rule", "description": "A rule to be applied in a Policy.", "type": "object", "properties": {"description": {"description": "Human-readable description of the rule.", "type": "string"}, "permissions": {"description": "A permission is a string of form `..` (e.g., 'storage.buckets.list'). A value of '*' matches all permissions, and a verb part of '*' (e.g., 'storage.buckets.*') matches all verbs.", "type": "array", "items": {"type": "string"}}, "action": {"description": "Required", "type": "string", "enumDescriptions": ["De<PERSON><PERSON> no action.", "Matching 'Entries' grant access.", "Matching 'Entries' grant access and the caller promises to log the request per the returned log_configs.", "Matching 'Entries' deny access.", "Matching 'Entries' deny access and the caller promises to log the request per the returned log_configs.", "Matching 'Entries' tell IAM.Check callers to generate logs."], "enum": ["NO_ACTION", "ALLOW", "ALLOW_WITH_LOG", "DENY", "DENY_WITH_LOG", "LOG"]}, "in": {"description": "If one or more 'in' clauses are specified, the rule matches if the PRINCIPAL/AUTHORITY_SELECTOR is in at least one of these entries.", "type": "array", "items": {"type": "string"}}, "notIn": {"description": "If one or more 'not_in' clauses are specified, the rule matches if the PRINCIPAL/AUTHORITY_SELECTOR is in none of the entries. The format for in and not_in entries can be found at in the Local IAM documentation (see go/local-iam#features).", "type": "array", "items": {"type": "string"}}, "conditions": {"description": "Additional restrictions that must be met. All conditions must pass for the rule to match.", "type": "array", "items": {"$ref": "Condition"}}, "logConfig": {"description": "The config returned to callers of CheckPolicy for any entries that match the LOG action.", "type": "array", "items": {"$ref": "LogConfig"}}}}, "Condition": {"id": "Condition", "description": "A condition to be met.", "type": "object", "properties": {"iam": {"description": "Trusted attributes supplied by the IAM system.", "type": "string", "enumDescriptions": ["Default non-attribute.", "Either principal or (if present) authority selector.", "The principal (even if an authority selector is present), which must only be used for attribution, not authorization.", "Any of the security realms in the IAMContext (go/security-realms). When used with IN, the condition indicates \"any of the request's realms match one of the given values; with NOT_IN, \"none of the realms match any of the given values\". Note that a value can be: - 'self:campus' (i.e., clients that are in the same campus) - 'self:metro' (i.e., clients that are in the same metro) - 'self:cloud-region' (i.e., allow connections from clients that are in the same cloud region) - 'self:prod-region' (i.e., allow connections from clients that are in the same prod region) - 'guardians' (i.e., allow connections from its guardian realms. See go/security-realms-glossary#guardian for more information.) - 'cryto_core_guardians' (i.e., allow connections from its crypto core guardian realms. See go/security-realms-glossary#guardian for more information.) Crypto Core coverage is a super-set of Default coverage, containing information about coverage between higher tier data centers (e.g., YAWNs). Most services should use Default coverage and only use Crypto Core coverage if the service is involved in greenfield turnup of new higher tier data centers (e.g., credential infrastructure, machine/job management systems, etc.). - 'self' [DEPRECATED] (i.e., allow connections from clients that are in the same security realm, which is currently but not guaranteed to be campus-sized) - a realm (e.g., 'campus-abc') - a realm group (e.g., 'realms-for-borg-cell-xx', see: go/realm-groups) A match is determined by a realm group membership check performed by a RealmAclRep object (go/realm-acl-howto). It is not permitted to grant access based on the *absence* of a realm, so realm conditions can only be used in a \"positive\" context (e.g., ALLOW/IN or DENY/NOT_IN).", "An approver (distinct from the requester) that has authorized this request. When used with IN, the condition indicates that one of the approvers associated with the request matches the specified principal, or is a member of the specified group. Approvers can only grant additional access, and are thus only used in a strictly positive context (e.g. ALLOW/IN or DENY/NOT_IN).", "What types of justifications have been supplied with this request. String values should match enum names from security.credentials.JustificationType, e.g. \"MANUAL_STRING\". It is not permitted to grant access based on the *absence* of a justification, so justification conditions can only be used in a \"positive\" context (e.g., ALLOW/IN or DENY/NOT_IN). Multiple justifications, e.g., a Buganizer ID and a manually-entered reason, are normal and supported.", "What type of credentials have been supplied with this request. String values should match enum names from security_loas_l2.CredentialsType - currently, only CREDS_TYPE_EMERGENCY is supported. It is not permitted to grant access based on the *absence* of a credentials type, so the conditions can only be used in a \"positive\" context (e.g., ALLOW/IN or DENY/NOT_IN).", "Properties of the credentials supplied with this request. See http://go/rpcsp-credential-assertions?polyglot=rpcsp-v1-0 The conditions can only be used in a \"positive\" context (e.g., ALLOW/IN or DENY/NOT_IN)."], "enum": ["NO_ATTR", "AUTHORITY", "ATTRIBUTION", "SECURITY_REALM", "APPROVER", "JUSTIFICATION_TYPE", "CREDENTIALS_TYPE", "CREDS_ASSERTION"]}, "sys": {"description": "Trusted attributes supplied by any service that owns resources and uses the IAM system for access control.", "type": "string", "enumDescriptions": ["Default non-attribute type", "Region of the resource", "Service name", "Resource name", "IP address of the caller"], "enum": ["NO_ATTR", "REGION", "SERVICE", "NAME", "IP"]}, "svc": {"description": "Trusted attributes discharged by the service.", "type": "string"}, "op": {"description": "An operator to apply the subject with.", "type": "string", "enumDescriptions": ["Default no-op.", "DEPRECATED. Use IN instead.", "DEPRECATED. Use NOT_IN instead.", "The condition is true if the subject (or any element of it if it is a set) matches any of the supplied values.", "The condition is true if the subject (or every element of it if it is a set) matches none of the supplied values.", "Subject is discharged"], "enum": ["NO_OP", "EQUALS", "NOT_EQUALS", "IN", "NOT_IN", "DISCHARGED"]}, "values": {"description": "The objects of the condition.", "type": "array", "items": {"type": "string"}}}}, "LogConfig": {"id": "LogConfig", "description": "Specifies what kind of log the caller must write", "type": "object", "properties": {"counter": {"description": "Counter options.", "$ref": "CounterOptions"}, "dataAccess": {"description": "Data access options.", "$ref": "DataAccessOptions"}, "cloudAudit": {"description": "Cloud audit options.", "$ref": "CloudAuditOptions"}}}, "CounterOptions": {"id": "CounterOptions", "description": "Increment a streamz counter with the specified metric and field names. Metric names should start with a '/', generally be lowercase-only, and end in \"_count\". Field names should not contain an initial slash. The actual exported metric names will have \"/iam/policy\" prepended. Field names correspond to IAM request parameters and field values are their respective values. Supported field names: - \"authority\", which is \"[token]\" if IAMContext.token is present, otherwise the value of IAMContext.authority_selector if present, and otherwise a representation of IAMContext.principal; or - \"iam_principal\", a representation of IAMContext.principal even if a token or authority selector is present; or - \"\" (empty string), resulting in a counter with no fields. Examples: counter { metric: \"/debug_access_count\" field: \"iam_principal\" } ==> increment counter /iam/policy/debug_access_count {iam_principal=[value of IAMContext.principal]}", "type": "object", "properties": {"metric": {"description": "The metric to update.", "type": "string"}, "field": {"description": "The field value to attribute.", "type": "string"}, "customFields": {"description": "Custom fields.", "type": "array", "items": {"$ref": "CustomField"}}}}, "CustomField": {"id": "CustomField", "description": "Custom fields. These can be used to create a counter with arbitrary field/value pairs. See: go/rpcsp-custom-fields.", "type": "object", "properties": {"name": {"description": "Name is the field name.", "type": "string"}, "value": {"description": "Value is the field value. It is important that in contrast to the CounterOptions.field, the value here is a constant that is not derived from the IAMContext.", "type": "string"}}}, "DataAccessOptions": {"id": "DataAccessOptions", "description": "Write a Data Access (Gin) log", "type": "object", "properties": {"logMode": {"type": "string", "enumDescriptions": ["Client is not required to write a partial Gin log immediately after the authorization check. If client chooses to write one and it fails, client may either fail open (allow the operation to continue) or fail closed (handle as a DENY outcome).", "The application's operation in the context of which this authorization check is being made may only be performed if it is successfully logged to Gin. For instance, the authorization library may satisfy this obligation by emitting a partial log entry at authorization check time and only returning ALLOW to the application if it succeeds. If a matching Rule has this directive, but the client has not indicated that it will honor such requirements, then the IAM check will result in authorization failure by setting CheckPolicyResponse.success=false."], "enum": ["LOG_MODE_UNSPECIFIED", "LOG_FAIL_CLOSED"]}, "isDirectAuth": {"description": "Indicates that access was granted by a regular grant policy", "type": "boolean"}}}, "CloudAuditOptions": {"id": "CloudAuditOptions", "description": "Write a Cloud Audit log", "type": "object", "properties": {"logName": {"description": "The log_name to populate in the Cloud Audit Record.", "type": "string", "enumDescriptions": ["Default. Should not be used.", "Corresponds to \"cloudaudit.googleapis.com/activity\"", "Corresponds to \"cloudaudit.googleapis.com/data_access\""], "enum": ["UNSPECIFIED_LOG_NAME", "ADMIN_ACTIVITY", "DATA_ACCESS"]}, "authorizationLoggingOptions": {"description": "Information used by the Cloud Audit Logging pipeline. Will be deprecated once the migration to PermissionType is complete (b/201806118).", "$ref": "AuthorizationLoggingOptions"}, "permissionType": {"description": "The type associated with the permission.", "type": "string", "enumDescriptions": ["Default. Should not be used.", "Permissions that gate reading resource configuration or metadata.", "Permissions that gate modification of resource configuration or metadata.", "Permissions that gate reading user-provided data.", "Permissions that gate writing user-provided data."], "enum": ["PERMISSION_TYPE_UNSPECIFIED", "ADMIN_READ", "ADMIN_WRITE", "DATA_READ", "DATA_WRITE"]}}}, "AuthorizationLoggingOptions": {"id": "AuthorizationLoggingOptions", "description": "Authorization-related information used by Cloud Audit Logging.", "type": "object", "properties": {"permissionType": {"description": "The type of the permission that was checked.", "type": "string", "enumDescriptions": ["Default. Should not be used.", "A read of admin (meta) data.", "A write of admin (meta) data.", "A read of standard data.", "A write of standard data."], "enum": ["PERMISSION_TYPE_UNSPECIFIED", "ADMIN_READ", "ADMIN_WRITE", "DATA_READ", "DATA_WRITE"]}}}, "SetIamPolicyRequest": {"id": "SetIamPolicyRequest", "description": "Request message for `SetIamPolicy` method.", "type": "object", "properties": {"policy": {"description": "REQUIRED: The complete policy to be applied to the `resource`. The size of the policy is limited to a few 10s of KB. An empty policy is a valid policy but certain Google Cloud services (such as Projects) might reject them.", "$ref": "Policy"}, "updateMask": {"description": "OPTIONAL: A FieldMask specifying which fields of the policy to modify. Only the fields in the mask will be modified. If no mask is provided, the following default mask is used: `paths: \"bindings, etag\"`", "type": "string", "format": "google-fieldmask"}}}, "TestIamPermissionsRequest": {"id": "TestIamPermissionsRequest", "description": "Request message for `TestIamPermissions` method.", "type": "object", "properties": {"permissions": {"description": "The set of permissions to check for the `resource`. Permissions with wildcards (such as `*` or `storage.*`) are not allowed. For more information see [IAM Overview](https://cloud.google.com/iam/docs/overview#permissions).", "type": "array", "items": {"type": "string"}}}}, "TestIamPermissionsResponse": {"id": "TestIamPermissionsResponse", "description": "Response message for `TestIamPermissions` method.", "type": "object", "properties": {"permissions": {"description": "A subset of `TestPermissionsRequest.permissions` that the caller is allowed.", "type": "array", "items": {"type": "string"}}}}, "ReservationGroup": {"id": "ReservationGroup", "description": "A reservation group is a container for reservations.", "type": "object", "properties": {"name": {"description": "Identifier. The resource name of the reservation group, e.g., `projects/*/locations/*/reservationGroups/team1-prod`. The reservation_group_id must only contain lower case alphanumeric characters or dashes. It must start with a letter and must not end with a dash. Its maximum length is 64 characters.", "type": "string"}}}, "ListReservationGroupsResponse": {"id": "ListReservationGroupsResponse", "description": "The response for ReservationService.ListReservationGroups.", "type": "object", "properties": {"reservationGroups": {"description": "List of reservations visible to the user.", "type": "array", "items": {"$ref": "ReservationGroup"}}, "nextPageToken": {"description": "Token to retrieve the next page of results, or empty if there are no more results in the list.", "type": "string"}}}}, "resources": {"projects": {"resources": {"locations": {"methods": {"searchAssignments": {"id": "bigqueryreservation.projects.locations.searchAssignments", "path": "v1/{+parent}:searchAssignments", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}:searchAssignments", "httpMethod": "GET", "parameters": {"parent": {"description": "Required. The resource name of the admin project(containing project and location), e.g.: `projects/myproject/locations/US`.", "pattern": "^projects/[^/]+/locations/[^/]+$", "location": "path", "required": true, "type": "string"}, "query": {"description": "Please specify resource name as assignee in the query. Examples: * `assignee=projects/myproject` * `assignee=folders/123` * `assignee=organizations/456`", "location": "query", "type": "string"}, "pageSize": {"description": "The maximum number of items to return per page.", "location": "query", "type": "integer", "format": "int32"}, "pageToken": {"description": "The next_page_token value returned from a previous List request, if any.", "location": "query", "type": "string"}}, "parameterOrder": ["parent"], "response": {"$ref": "SearchAssignmentsResponse"}, "scopes": ["https://www.googleapis.com/auth/bigquery", "https://www.googleapis.com/auth/cloud-platform"], "deprecated": true, "description": "Deprecated: Looks up assignments for a specified resource for a particular region. If the request is about a project: 1. Assignments created on the project will be returned if they exist. 2. Otherwise assignments created on the closest ancestor will be returned. 3. Assignments for different JobTypes will all be returned. The same logic applies if the request is about a folder. If the request is about an organization, then assignments created on the organization will be returned (organization doesn't have ancestors). Comparing to ListAssignments, there are some behavior differences: 1. permission on the assignee will be verified in this API. 2. Hierarchy lookup (project->folder->organization) happens in this API. 3. Parent here is `projects/*/locations/*`, instead of `projects/*/locations/*reservations/*`. **Note** \"-\" cannot be used for projects nor locations."}, "searchAllAssignments": {"id": "bigqueryreservation.projects.locations.searchAllAssignments", "path": "v1/{+parent}:searchAllAssignments", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}:searchAllAssignments", "httpMethod": "GET", "parameters": {"parent": {"description": "Required. The resource name with location (project name could be the wildcard '-'), e.g.: `projects/-/locations/US`.", "pattern": "^projects/[^/]+/locations/[^/]+$", "location": "path", "required": true, "type": "string"}, "query": {"description": "Please specify resource name as assignee in the query. Examples: * `assignee=projects/myproject` * `assignee=folders/123` * `assignee=organizations/456`", "location": "query", "type": "string"}, "pageSize": {"description": "The maximum number of items to return per page.", "location": "query", "type": "integer", "format": "int32"}, "pageToken": {"description": "The next_page_token value returned from a previous List request, if any.", "location": "query", "type": "string"}}, "parameterOrder": ["parent"], "response": {"$ref": "SearchAllAssignmentsResponse"}, "scopes": ["https://www.googleapis.com/auth/bigquery", "https://www.googleapis.com/auth/cloud-platform"], "description": "Looks up assignments for a specified resource for a particular region. If the request is about a project: 1. Assignments created on the project will be returned if they exist. 2. Otherwise assignments created on the closest ancestor will be returned. 3. Assignments for different JobTypes will all be returned. The same logic applies if the request is about a folder. If the request is about an organization, then assignments created on the organization will be returned (organization doesn't have ancestors). Comparing to ListAssignments, there are some behavior differences: 1. permission on the assignee will be verified in this API. 2. Hierarchy lookup (project->folder->organization) happens in this API. 3. Parent here is `projects/*/locations/*`, instead of `projects/*/locations/*reservations/*`."}, "getBiReservation": {"id": "bigqueryreservation.projects.locations.getBiReservation", "path": "v1/{+name}", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/biReservation", "httpMethod": "GET", "parameters": {"name": {"description": "Required. Name of the requested reservation, for example: `projects/{project_id}/locations/{location_id}/biReservation`", "pattern": "^projects/[^/]+/locations/[^/]+/biReservation$", "location": "path", "required": true, "type": "string"}}, "parameterOrder": ["name"], "response": {"$ref": "BiReservation"}, "scopes": ["https://www.googleapis.com/auth/bigquery", "https://www.googleapis.com/auth/cloud-platform"], "description": "Retrieves a BI reservation."}, "updateBiReservation": {"id": "bigqueryreservation.projects.locations.updateBiReservation", "path": "v1/{+name}", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/biReservation", "httpMethod": "PATCH", "parameters": {"name": {"description": "Identifier. The resource name of the singleton BI reservation. Reservation names have the form `projects/{project_id}/locations/{location_id}/biReservation`.", "pattern": "^projects/[^/]+/locations/[^/]+/biReservation$", "location": "path", "required": true, "type": "string"}, "updateMask": {"description": "A list of fields to be updated in this request.", "location": "query", "type": "string", "format": "google-fieldmask"}}, "parameterOrder": ["name"], "request": {"$ref": "BiReservation"}, "response": {"$ref": "BiReservation"}, "scopes": ["https://www.googleapis.com/auth/bigquery", "https://www.googleapis.com/auth/cloud-platform"], "description": "Updates a BI reservation. Only fields specified in the `field_mask` are updated. A singleton BI reservation always exists with default size 0. In order to reserve BI capacity it needs to be updated to an amount greater than 0. In order to release BI capacity reservation size must be set to 0."}, "upgradeEdition": {"id": "bigqueryreservation.projects.locations.upgradeEdition", "path": "v1/{+parent}:upgradeEdition", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}:upgradeEdition", "httpMethod": "POST", "parameters": {"parent": {"description": "Parent resource that identifies admin project and location e.g., `projects/myproject/locations/us`", "pattern": "^projects/[^/]+/locations/[^/]+$", "location": "path", "required": true, "type": "string"}}, "parameterOrder": ["parent"], "request": {"$ref": "UpgradeEditionRequest"}, "response": {"$ref": "UpgradeEditionResponse"}, "scopes": ["https://www.googleapis.com/auth/bigquery", "https://www.googleapis.com/auth/cloud-platform"], "description": "Upgrade the Edition for a list of reservations and capacity commitments. Edition can only be upgraded (From STANDARD to ENTERPRISE/ENTERPRISE_PLUS, from ENTERPRISE to ENTERPRISE_PLUS, from EDITION_UNSPECIFIED to ENTERPRISE_PLUS). Please make sure this won't cause slot increase in both original and upgraded edition. E.g. only upgrade the reservation but leave the commitment untouched. Otherwise, RPC will fail with the error code `google.rpc.Code.FAILED_PRECONDITION`. Restrications of the target Edition will be checked before the upgrade."}}, "resources": {"reservations": {"methods": {"create": {"id": "bigqueryreservation.projects.locations.reservations.create", "path": "v1/{+parent}/reservations", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/reservations", "httpMethod": "POST", "parameters": {"parent": {"description": "Required. Project, location. E.g., `projects/myproject/locations/US`", "pattern": "^projects/[^/]+/locations/[^/]+$", "location": "path", "required": true, "type": "string"}, "reservationId": {"description": "The reservation ID. It must only contain lower case alphanumeric characters or dashes. It must start with a letter and must not end with a dash. Its maximum length is 64 characters.", "location": "query", "type": "string"}, "useFrozenReservations": {"description": "If true, allow use of frozen reservations for this action. This is an internal-only field intended to migrate existing reservations on to reservation service.", "location": "query", "type": "boolean"}}, "parameterOrder": ["parent"], "request": {"$ref": "Reservation"}, "response": {"$ref": "Reservation"}, "scopes": ["https://www.googleapis.com/auth/bigquery", "https://www.googleapis.com/auth/cloud-platform"], "description": "Creates a new reservation resource."}, "list": {"id": "bigqueryreservation.projects.locations.reservations.list", "path": "v1/{+parent}/reservations", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/reservations", "httpMethod": "GET", "parameters": {"parent": {"description": "Required. The parent resource name containing project and location, e.g.: `projects/myproject/locations/US`", "pattern": "^projects/[^/]+/locations/[^/]+$", "location": "path", "required": true, "type": "string"}, "pageSize": {"description": "The maximum number of items to return per page.", "location": "query", "type": "integer", "format": "int32"}, "pageToken": {"description": "The next_page_token value returned from a previous List request, if any.", "location": "query", "type": "string"}, "filter": {"description": "Optional. The filter to apply to the list results. For now, only filter by reservation group is supported. Examples: \"reservation_group=my-group\"", "location": "query", "type": "string"}}, "parameterOrder": ["parent"], "response": {"$ref": "ListReservationsResponse"}, "scopes": ["https://www.googleapis.com/auth/bigquery", "https://www.googleapis.com/auth/cloud-platform"], "description": "Lists all the reservations for the project in the specified location."}, "get": {"id": "bigqueryreservation.projects.locations.reservations.get", "path": "v1/{+name}", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/reservations/{reservationsId}", "httpMethod": "GET", "parameters": {"name": {"description": "Required. Resource name of the reservation to retrieve. E.g., `projects/myproject/locations/US/reservations/team1-prod`", "pattern": "^projects/[^/]+/locations/[^/]+/reservations/[^/]+$", "location": "path", "required": true, "type": "string"}}, "parameterOrder": ["name"], "response": {"$ref": "Reservation"}, "scopes": ["https://www.googleapis.com/auth/bigquery", "https://www.googleapis.com/auth/cloud-platform"], "description": "Returns information about the reservation."}, "delete": {"id": "bigqueryreservation.projects.locations.reservations.delete", "path": "v1/{+name}", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/reservations/{reservationsId}", "httpMethod": "DELETE", "parameters": {"name": {"description": "Required. Resource name of the reservation to retrieve. E.g., `projects/myproject/locations/US/reservations/team1-prod`", "pattern": "^projects/[^/]+/locations/[^/]+/reservations/[^/]+$", "location": "path", "required": true, "type": "string"}, "useFrozenReservations": {"description": "If true, allow use of frozen reservations for this action. This is an internal-only field intended to migrate existing reservations on to reservation service.", "location": "query", "type": "boolean"}, "force": {"description": "Can be used to force delete reservation even if external jobs are running.", "location": "query", "type": "boolean"}}, "parameterOrder": ["name"], "response": {"$ref": "Empty"}, "scopes": ["https://www.googleapis.com/auth/bigquery", "https://www.googleapis.com/auth/cloud-platform"], "description": "Deletes a reservation. Returns `google.rpc.Code.FAILED_PRECONDITION` when reservation has assignments."}, "patch": {"id": "bigqueryreservation.projects.locations.reservations.patch", "path": "v1/{+name}", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/reservations/{reservationsId}", "httpMethod": "PATCH", "parameters": {"name": {"description": "Identifier. The resource name of the reservation, e.g., `projects/*/locations/*/reservations/team1-prod`. The reservation_id must only contain lower case alphanumeric characters or dashes. It must start with a letter and must not end with a dash. Its maximum length is 64 characters.", "pattern": "^projects/[^/]+/locations/[^/]+/reservations/[^/]+$", "location": "path", "required": true, "type": "string"}, "updateMask": {"description": "Standard field mask for the set of fields to be updated.", "location": "query", "type": "string", "format": "google-fieldmask"}, "useFrozenReservations": {"description": "If true, allow use of frozen reservations for this action. This is an internal-only field intended to migrate existing reservations on to reservation service.", "location": "query", "type": "boolean"}}, "parameterOrder": ["name"], "request": {"$ref": "Reservation"}, "response": {"$ref": "Reservation"}, "scopes": ["https://www.googleapis.com/auth/bigquery", "https://www.googleapis.com/auth/cloud-platform"], "description": "Updates an existing reservation resource."}, "failoverReservation": {"id": "bigqueryreservation.projects.locations.reservations.failoverReservation", "path": "v1/{+name}:failoverReservation", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/reservations/{reservationsId}:failoverReservation", "httpMethod": "POST", "parameters": {"name": {"description": "Required. Resource name of the reservation to failover. E.g., `projects/myproject/locations/US/reservations/team1-prod`", "pattern": "^projects/[^/]+/locations/[^/]+/reservations/[^/]+$", "location": "path", "required": true, "type": "string"}}, "parameterOrder": ["name"], "request": {"$ref": "FailoverReservationRequest"}, "response": {"$ref": "Reservation"}, "scopes": ["https://www.googleapis.com/auth/bigquery", "https://www.googleapis.com/auth/cloud-platform"], "description": "Fail over a reservation to the secondary location. The operation should be done in the current secondary location, which will be promoted to the new primary location for the reservation. Attempting to failover a reservation in the current primary location will fail with the error code `google.rpc.Code.FAILED_PRECONDITION`."}, "getIamPolicy": {"id": "bigqueryreservation.projects.locations.reservations.getIamPolicy", "path": "v1/{+resource}:getIamPolicy", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/reservations/{reservationsId}:getIamPolicy", "httpMethod": "GET", "parameters": {"resource": {"description": "REQUIRED: The resource for which the policy is being requested. See [Resource names](https://cloud.google.com/apis/design/resource_names) for the appropriate value for this field.", "pattern": "^projects/[^/]+/locations/[^/]+/reservations/[^/]+$", "location": "path", "required": true, "type": "string"}, "options.requestedPolicyVersion": {"description": "Optional. The maximum policy version that will be used to format the policy. Valid values are 0, 1, and 3. Requests specifying an invalid value will be rejected. Requests for policies with any conditional role bindings must specify version 3. Policies with no conditional role bindings may specify any valid value or leave the field unset. The policy in the response might use the policy version that you specified, or it might use a lower policy version. For example, if you specify version 3, but the policy has no conditional role bindings, the response uses version 1. To learn which resources support conditions in their IAM policies, see the [IAM documentation](https://cloud.google.com/iam/help/conditions/resource-policies).", "location": "query", "type": "integer", "format": "int32"}}, "parameterOrder": ["resource"], "response": {"$ref": "Policy"}, "scopes": ["https://www.googleapis.com/auth/bigquery", "https://www.googleapis.com/auth/cloud-platform"], "description": "Gets the access control policy for a resource. May return: * A`NOT_FOUND` error if the resource doesn't exist or you don't have the permission to view it. * An empty policy if the resource exists but doesn't have a set policy. Supported resources are: - Reservations - ReservationAssignments To call this method, you must have the following Google IAM permissions: - `bigqueryreservation.reservations.getIamPolicy` to get policies on reservations."}, "setIamPolicy": {"id": "bigqueryreservation.projects.locations.reservations.setIamPolicy", "path": "v1/{+resource}:setIamPolicy", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/reservations/{reservationsId}:setIamPolicy", "httpMethod": "POST", "parameters": {"resource": {"description": "REQUIRED: The resource for which the policy is being specified. See [Resource names](https://cloud.google.com/apis/design/resource_names) for the appropriate value for this field.", "pattern": "^projects/[^/]+/locations/[^/]+/reservations/[^/]+$", "location": "path", "required": true, "type": "string"}}, "parameterOrder": ["resource"], "request": {"$ref": "SetIamPolicyRequest"}, "response": {"$ref": "Policy"}, "scopes": ["https://www.googleapis.com/auth/bigquery", "https://www.googleapis.com/auth/cloud-platform"], "description": "Sets an access control policy for a resource. Replaces any existing policy. Supported resources are: - Reservations To call this method, you must have the following Google IAM permissions: - `bigqueryreservation.reservations.setIamPolicy` to set policies on reservations."}, "testIamPermissions": {"id": "bigqueryreservation.projects.locations.reservations.testIamPermissions", "path": "v1/{+resource}:testIamPermissions", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/reservations/{reservationsId}:testIamPermissions", "httpMethod": "POST", "parameters": {"resource": {"description": "REQUIRED: The resource for which the policy detail is being requested. See [Resource names](https://cloud.google.com/apis/design/resource_names) for the appropriate value for this field.", "pattern": "^projects/[^/]+/locations/[^/]+/reservations/[^/]+$", "location": "path", "required": true, "type": "string"}}, "parameterOrder": ["resource"], "request": {"$ref": "TestIamPermissionsRequest"}, "response": {"$ref": "TestIamPermissionsResponse"}, "scopes": ["https://www.googleapis.com/auth/bigquery", "https://www.googleapis.com/auth/cloud-platform"], "description": "Gets your permissions on a resource. Returns an empty set of permissions if the resource doesn't exist. Supported resources are: - Reservations No Google IAM permissions are required to call this method."}}, "resources": {"assignments": {"methods": {"create": {"id": "bigqueryreservation.projects.locations.reservations.assignments.create", "path": "v1/{+parent}/assignments", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/reservations/{reservationsId}/assignments", "httpMethod": "POST", "parameters": {"parent": {"description": "Required. The parent resource name of the assignment E.g. `projects/myproject/locations/US/reservations/team1-prod`", "pattern": "^projects/[^/]+/locations/[^/]+/reservations/[^/]+$", "location": "path", "required": true, "type": "string"}, "useFrozenReservations": {"description": "If true, allow use of frozen reservations for this action. This is an internal-only field intended to migrate existing reservations on to reservation service.", "location": "query", "type": "boolean"}, "assignmentId": {"description": "The optional assignment ID. Assignment name will be generated automatically if this field is empty. This field must only contain lower case alphanumeric characters or dashes. Max length is 64 characters.", "location": "query", "type": "string"}}, "parameterOrder": ["parent"], "request": {"$ref": "Assignment"}, "response": {"$ref": "Assignment"}, "scopes": ["https://www.googleapis.com/auth/bigquery", "https://www.googleapis.com/auth/cloud-platform"], "description": "Creates an assignment object which allows the given project to submit jobs of a certain type using slots from the specified reservation. Currently a resource (project, folder, organization) can only have one assignment per each (job_type, location) combination, and that reservation will be used for all jobs of the matching type. Different assignments can be created on different levels of the projects, folders or organization hierarchy. During query execution, the assignment is looked up at the project, folder and organization levels in that order. The first assignment found is applied to the query. When creating assignments, it does not matter if other assignments exist at higher levels. Example: * The organization `organizationA` contains two projects, `project1` and `project2`. * Assignments for all three entities (`organizationA`, `project1`, and `project2`) could all be created and mapped to the same or different reservations. \"None\" assignments represent an absence of the assignment. Projects assigned to None use on-demand pricing. To create a \"None\" assignment, use \"none\" as a reservation_id in the parent. Example parent: `projects/myproject/locations/US/reservations/none`. Returns `google.rpc.Code.PERMISSION_DENIED` if user does not have 'bigquery.admin' permissions on the project using the reservation and the project that owns this reservation. Returns `google.rpc.Code.INVALID_ARGUMENT` when location of the assignment does not match location of the reservation."}, "list": {"id": "bigqueryreservation.projects.locations.reservations.assignments.list", "path": "v1/{+parent}/assignments", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/reservations/{reservationsId}/assignments", "httpMethod": "GET", "parameters": {"parent": {"description": "Required. The parent resource name e.g.: `projects/myproject/locations/US/reservations/team1-prod` Or: `projects/myproject/locations/US/reservations/-`", "pattern": "^projects/[^/]+/locations/[^/]+/reservations/[^/]+$", "location": "path", "required": true, "type": "string"}, "pageSize": {"description": "The maximum number of items to return per page.", "location": "query", "type": "integer", "format": "int32"}, "pageToken": {"description": "The next_page_token value returned from a previous List request, if any.", "location": "query", "type": "string"}}, "parameterOrder": ["parent"], "response": {"$ref": "ListAssignmentsResponse"}, "scopes": ["https://www.googleapis.com/auth/bigquery", "https://www.googleapis.com/auth/cloud-platform"], "description": "Lists assignments. Only explicitly created assignments will be returned. Example: * Organization `organizationA` contains two projects, `project1` and `project2`. * Reservation `res1` exists and was created previously. * CreateAssignment was used previously to define the following associations between entities and reservations: `` and `` In this example, ListAssignments will just return the above two assignments for reservation `res1`, and no expansion/merge will happen. The wildcard \"-\" can be used for reservations in the request. In that case all assignments belongs to the specified project and location will be listed. **Note** \"-\" cannot be used for projects nor locations."}, "delete": {"id": "bigqueryreservation.projects.locations.reservations.assignments.delete", "path": "v1/{+name}", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/reservations/{reservationsId}/assignments/{assignmentsId}", "httpMethod": "DELETE", "parameters": {"name": {"description": "Required. Name of the resource, e.g. `projects/myproject/locations/US/reservations/team1-prod/assignments/123`", "pattern": "^projects/[^/]+/locations/[^/]+/reservations/[^/]+/assignments/[^/]+$", "location": "path", "required": true, "type": "string"}, "useFrozenReservations": {"description": "If true, allow use of frozen reservations for this action. This is an internal-only field intended to migrate existing reservations on to reservation service.", "location": "query", "type": "boolean"}}, "parameterOrder": ["name"], "response": {"$ref": "Empty"}, "scopes": ["https://www.googleapis.com/auth/bigquery", "https://www.googleapis.com/auth/cloud-platform"], "description": "Deletes a assignment. No expansion will happen. Example: * Organization `organizationA` contains two projects, `project1` and `project2`. * Reservation `res1` exists and was created previously. * CreateAssignment was used previously to define the following associations between entities and reservations: `` and `` In this example, deletion of the `` assignment won't affect the other assignment ``. After said deletion, queries from `project1` will still use `res1` while queries from `project2` will switch to use on-demand mode."}, "move": {"id": "bigqueryreservation.projects.locations.reservations.assignments.move", "path": "v1/{+name}:move", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/reservations/{reservationsId}/assignments/{assignmentsId}:move", "httpMethod": "POST", "parameters": {"name": {"description": "Required. The resource name of the assignment, e.g. `projects/myproject/locations/US/reservations/team1-prod/assignments/123`", "pattern": "^projects/[^/]+/locations/[^/]+/reservations/[^/]+/assignments/[^/]+$", "location": "path", "required": true, "type": "string"}}, "parameterOrder": ["name"], "request": {"$ref": "MoveAssignmentRequest"}, "response": {"$ref": "Assignment"}, "scopes": ["https://www.googleapis.com/auth/bigquery", "https://www.googleapis.com/auth/cloud-platform"], "description": "Moves an assignment under a new reservation. This differs from removing an existing assignment and recreating a new one by providing a transactional change that ensures an assignee always has an associated reservation."}, "patch": {"id": "bigqueryreservation.projects.locations.reservations.assignments.patch", "path": "v1/{+name}", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/reservations/{reservationsId}/assignments/{assignmentsId}", "httpMethod": "PATCH", "parameters": {"name": {"description": "Output only. Name of the resource. E.g.: `projects/myproject/locations/US/reservations/team1-prod/assignments/123`. The assignment_id must only contain lower case alphanumeric characters or dashes and the max length is 64 characters.", "pattern": "^projects/[^/]+/locations/[^/]+/reservations/[^/]+/assignments/[^/]+$", "location": "path", "required": true, "type": "string"}, "updateMask": {"description": "Standard field mask for the set of fields to be updated.", "location": "query", "type": "string", "format": "google-fieldmask"}, "useFrozenReservations": {"description": "If true, allow use of frozen reservations for this action. This is an internal-only field intended to migrate existing reservations on to reservation service.", "location": "query", "type": "boolean"}}, "parameterOrder": ["name"], "request": {"$ref": "Assignment"}, "response": {"$ref": "Assignment"}, "scopes": ["https://www.googleapis.com/auth/bigquery", "https://www.googleapis.com/auth/cloud-platform"], "description": "Updates an existing assignment. Only the `priority` field can be updated."}, "getIamPolicy": {"id": "bigqueryreservation.projects.locations.reservations.assignments.getIamPolicy", "path": "v1/{+resource}:getIamPolicy", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/reservations/{reservationsId}/assignments/{assignmentsId}:getIamPolicy", "httpMethod": "GET", "parameters": {"resource": {"description": "REQUIRED: The resource for which the policy is being requested. See [Resource names](https://cloud.google.com/apis/design/resource_names) for the appropriate value for this field.", "pattern": "^projects/[^/]+/locations/[^/]+/reservations/[^/]+/assignments/[^/]+$", "location": "path", "required": true, "type": "string"}, "options.requestedPolicyVersion": {"description": "Optional. The maximum policy version that will be used to format the policy. Valid values are 0, 1, and 3. Requests specifying an invalid value will be rejected. Requests for policies with any conditional role bindings must specify version 3. Policies with no conditional role bindings may specify any valid value or leave the field unset. The policy in the response might use the policy version that you specified, or it might use a lower policy version. For example, if you specify version 3, but the policy has no conditional role bindings, the response uses version 1. To learn which resources support conditions in their IAM policies, see the [IAM documentation](https://cloud.google.com/iam/help/conditions/resource-policies).", "location": "query", "type": "integer", "format": "int32"}}, "parameterOrder": ["resource"], "response": {"$ref": "Policy"}, "scopes": ["https://www.googleapis.com/auth/bigquery", "https://www.googleapis.com/auth/cloud-platform"], "description": "Gets the access control policy for a resource. May return: * A`NOT_FOUND` error if the resource doesn't exist or you don't have the permission to view it. * An empty policy if the resource exists but doesn't have a set policy. Supported resources are: - Reservations - ReservationAssignments To call this method, you must have the following Google IAM permissions: - `bigqueryreservation.reservations.getIamPolicy` to get policies on reservations."}, "setIamPolicy": {"id": "bigqueryreservation.projects.locations.reservations.assignments.setIamPolicy", "path": "v1/{+resource}:setIamPolicy", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/reservations/{reservationsId}/assignments/{assignmentsId}:setIamPolicy", "httpMethod": "POST", "parameters": {"resource": {"description": "REQUIRED: The resource for which the policy is being specified. See [Resource names](https://cloud.google.com/apis/design/resource_names) for the appropriate value for this field.", "pattern": "^projects/[^/]+/locations/[^/]+/reservations/[^/]+/assignments/[^/]+$", "location": "path", "required": true, "type": "string"}}, "parameterOrder": ["resource"], "request": {"$ref": "SetIamPolicyRequest"}, "response": {"$ref": "Policy"}, "scopes": ["https://www.googleapis.com/auth/bigquery", "https://www.googleapis.com/auth/cloud-platform"], "description": "Sets an access control policy for a resource. Replaces any existing policy. Supported resources are: - Reservations To call this method, you must have the following Google IAM permissions: - `bigqueryreservation.reservations.setIamPolicy` to set policies on reservations."}, "testIamPermissions": {"id": "bigqueryreservation.projects.locations.reservations.assignments.testIamPermissions", "path": "v1/{+resource}:testIamPermissions", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/reservations/{reservationsId}/assignments/{assignmentsId}:testIamPermissions", "httpMethod": "POST", "parameters": {"resource": {"description": "REQUIRED: The resource for which the policy detail is being requested. See [Resource names](https://cloud.google.com/apis/design/resource_names) for the appropriate value for this field.", "pattern": "^projects/[^/]+/locations/[^/]+/reservations/[^/]+/assignments/[^/]+$", "location": "path", "required": true, "type": "string"}}, "parameterOrder": ["resource"], "request": {"$ref": "TestIamPermissionsRequest"}, "response": {"$ref": "TestIamPermissionsResponse"}, "scopes": ["https://www.googleapis.com/auth/bigquery", "https://www.googleapis.com/auth/cloud-platform"], "description": "Gets your permissions on a resource. Returns an empty set of permissions if the resource doesn't exist. Supported resources are: - Reservations No Google IAM permissions are required to call this method."}}}}}, "capacityCommitments": {"methods": {"create": {"id": "bigqueryreservation.projects.locations.capacityCommitments.create", "path": "v1/{+parent}/capacityCommitments", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/capacityCommitments", "httpMethod": "POST", "parameters": {"parent": {"description": "Required. Resource name of the parent reservation. E.g., `projects/myproject/locations/US`", "pattern": "^projects/[^/]+/locations/[^/]+$", "location": "path", "required": true, "type": "string"}, "useFrozenCapacityCommitments": {"description": "If true, allow use of capacity commitments for this action. This is an internal-only field intended to migrate existing reservations on to reservation service.", "location": "query", "type": "boolean"}, "enforceSingleAdminProjectPerOrg": {"description": "If true, fail the request if another project in the organization has a capacity commitment.", "location": "query", "type": "boolean"}, "capacityCommitmentId": {"description": "The optional capacity commitment ID. Capacity commitment name will be generated automatically if this field is empty. This field must only contain lower case alphanumeric characters or dashes. The first and last character cannot be a dash. Max length is 64 characters. NOTE: this ID won't be kept if the capacity commitment is split or merged.", "location": "query", "type": "string"}}, "parameterOrder": ["parent"], "request": {"$ref": "CapacityCommitment"}, "response": {"$ref": "CapacityCommitment"}, "scopes": ["https://www.googleapis.com/auth/bigquery", "https://www.googleapis.com/auth/cloud-platform"], "description": "Creates a new capacity commitment resource."}, "list": {"id": "bigqueryreservation.projects.locations.capacityCommitments.list", "path": "v1/{+parent}/capacityCommitments", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/capacityCommitments", "httpMethod": "GET", "parameters": {"parent": {"description": "Required. Resource name of the parent reservation. E.g., `projects/myproject/locations/US`", "pattern": "^projects/[^/]+/locations/[^/]+$", "location": "path", "required": true, "type": "string"}, "pageSize": {"description": "The maximum number of items to return.", "location": "query", "type": "integer", "format": "int32"}, "pageToken": {"description": "The next_page_token value returned from a previous List request, if any.", "location": "query", "type": "string"}}, "parameterOrder": ["parent"], "response": {"$ref": "ListCapacityCommitmentsResponse"}, "scopes": ["https://www.googleapis.com/auth/bigquery", "https://www.googleapis.com/auth/cloud-platform"], "description": "Lists all the capacity commitments for the admin project."}, "get": {"id": "bigqueryreservation.projects.locations.capacityCommitments.get", "path": "v1/{+name}", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/capacityCommitments/{capacityCommitmentsId}", "httpMethod": "GET", "parameters": {"name": {"description": "Required. Resource name of the capacity commitment to retrieve. E.g., `projects/myproject/locations/US/capacityCommitments/123`", "pattern": "^projects/[^/]+/locations/[^/]+/capacityCommitments/[^/]+$", "location": "path", "required": true, "type": "string"}}, "parameterOrder": ["name"], "response": {"$ref": "CapacityCommitment"}, "scopes": ["https://www.googleapis.com/auth/bigquery", "https://www.googleapis.com/auth/cloud-platform"], "description": "Returns information about the capacity commitment."}, "delete": {"id": "bigqueryreservation.projects.locations.capacityCommitments.delete", "path": "v1/{+name}", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/capacityCommitments/{capacityCommitmentsId}", "httpMethod": "DELETE", "parameters": {"name": {"description": "Required. Resource name of the capacity commitment to delete. E.g., `projects/myproject/locations/US/capacityCommitments/123`", "pattern": "^projects/[^/]+/locations/[^/]+/capacityCommitments/[^/]+$", "location": "path", "required": true, "type": "string"}, "useFrozenCapacityCommitments": {"description": "If true, allow use of capacity commitments for this action. This is an internal-only field intended to migrate existing reservations on to reservation service.", "location": "query", "type": "boolean"}, "force": {"description": "Can be used to force delete commitments even if assignments exist. Deleting commitments with assignments may cause queries to fail if they no longer have access to slots.", "location": "query", "type": "boolean"}}, "parameterOrder": ["name"], "response": {"$ref": "Empty"}, "scopes": ["https://www.googleapis.com/auth/bigquery", "https://www.googleapis.com/auth/cloud-platform"], "description": "Deletes a capacity commitment. Attempting to delete capacity commitment before its commitment_end_time will fail with the error code `google.rpc.Code.FAILED_PRECONDITION`."}, "patch": {"id": "bigqueryreservation.projects.locations.capacityCommitments.patch", "path": "v1/{+name}", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/capacityCommitments/{capacityCommitmentsId}", "httpMethod": "PATCH", "parameters": {"name": {"description": "Output only. The resource name of the capacity commitment, e.g., `projects/myproject/locations/US/capacityCommitments/123` The commitment_id must only contain lower case alphanumeric characters or dashes. It must start with a letter and must not end with a dash. Its maximum length is 64 characters.", "pattern": "^projects/[^/]+/locations/[^/]+/capacityCommitments/[^/]+$", "location": "path", "required": true, "type": "string"}, "updateMask": {"description": "Standard field mask for the set of fields to be updated.", "location": "query", "type": "string", "format": "google-fieldmask"}}, "parameterOrder": ["name"], "request": {"$ref": "CapacityCommitment"}, "response": {"$ref": "CapacityCommitment"}, "scopes": ["https://www.googleapis.com/auth/bigquery", "https://www.googleapis.com/auth/cloud-platform"], "description": "Updates an existing capacity commitment. Only `plan` and `renewal_plan` fields can be updated. Plan can only be changed to a plan of a longer commitment period. Attempting to change to a plan with shorter commitment period will fail with the error code `google.rpc.Code.FAILED_PRECONDITION`."}, "split": {"id": "bigqueryreservation.projects.locations.capacityCommitments.split", "path": "v1/{+name}:split", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/capacityCommitments/{capacityCommitmentsId}:split", "httpMethod": "POST", "parameters": {"name": {"description": "Required. The resource name e.g.,: `projects/myproject/locations/US/capacityCommitments/123`", "pattern": "^projects/[^/]+/locations/[^/]+/capacityCommitments/[^/]+$", "location": "path", "required": true, "type": "string"}}, "parameterOrder": ["name"], "request": {"$ref": "SplitCapacityCommitmentRequest"}, "response": {"$ref": "SplitCapacityCommitmentResponse"}, "scopes": ["https://www.googleapis.com/auth/bigquery", "https://www.googleapis.com/auth/cloud-platform"], "description": "Splits capacity commitment to two commitments of the same plan and `commitment_end_time`. A common use case is to enable downgrading commitments. For example, in order to downgrade from 10000 slots to 8000, you might split a 10000 capacity commitment into commitments of 2000 and 8000. Then, you delete the first one after the commitment end time passes."}, "merge": {"id": "bigqueryreservation.projects.locations.capacityCommitments.merge", "path": "v1/{+parent}/capacityCommitments:merge", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/capacityCommitments:merge", "httpMethod": "POST", "parameters": {"parent": {"description": "Parent resource that identifies admin project and location e.g., `projects/myproject/locations/us`", "pattern": "^projects/[^/]+/locations/[^/]+$", "location": "path", "required": true, "type": "string"}}, "parameterOrder": ["parent"], "request": {"$ref": "MergeCapacityCommitmentsRequest"}, "response": {"$ref": "CapacityCommitment"}, "scopes": ["https://www.googleapis.com/auth/bigquery", "https://www.googleapis.com/auth/cloud-platform"], "description": "Merges capacity commitments of the same plan into a single commitment. The resulting capacity commitment has the greater commitment_end_time out of the to-be-merged capacity commitments. Attempting to merge capacity commitments of different plan will fail with the error code `google.rpc.Code.FAILED_PRECONDITION`."}}}, "reservationGroups": {"methods": {"create": {"id": "bigqueryreservation.projects.locations.reservationGroups.create", "path": "v1/{+parent}/reservationGroups", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/reservationGroups", "httpMethod": "POST", "parameters": {"parent": {"description": "Required. Project, location. E.g., `projects/myproject/locations/US`", "pattern": "^projects/[^/]+/locations/[^/]+$", "location": "path", "required": true, "type": "string"}, "reservationGroupId": {"description": "Required. The reservation group ID. It must only contain lower case alphanumeric characters or dashes. It must start with a letter and must not end with a dash. Its maximum length is 64 characters.", "location": "query", "type": "string"}}, "parameterOrder": ["parent"], "request": {"$ref": "ReservationGroup"}, "response": {"$ref": "ReservationGroup"}, "scopes": ["https://www.googleapis.com/auth/bigquery", "https://www.googleapis.com/auth/cloud-platform"], "description": "Creates a new reservation group."}, "get": {"id": "bigqueryreservation.projects.locations.reservationGroups.get", "path": "v1/{+name}", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/reservationGroups/{reservationGroupsId}", "httpMethod": "GET", "parameters": {"name": {"pattern": "^projects/[^/]+/locations/[^/]+/reservationGroups/[^/]+$", "location": "path", "required": true, "type": "string"}}, "parameterOrder": ["name"], "response": {"$ref": "ReservationGroup"}, "scopes": ["https://www.googleapis.com/auth/bigquery", "https://www.googleapis.com/auth/cloud-platform"], "description": "Returns information about the reservation group."}, "delete": {"id": "bigqueryreservation.projects.locations.reservationGroups.delete", "path": "v1/{+name}", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/reservationGroups/{reservationGroupsId}", "httpMethod": "DELETE", "parameters": {"name": {"pattern": "^projects/[^/]+/locations/[^/]+/reservationGroups/[^/]+$", "location": "path", "required": true, "type": "string"}}, "parameterOrder": ["name"], "response": {"$ref": "Empty"}, "scopes": ["https://www.googleapis.com/auth/bigquery", "https://www.googleapis.com/auth/cloud-platform"], "description": "Deletes a reservation. Returns `google.rpc.Code.FAILED_PRECONDITION` when reservation has assignments."}, "list": {"id": "bigqueryreservation.projects.locations.reservationGroups.list", "path": "v1/{+parent}/reservationGroups", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/reservationGroups", "httpMethod": "GET", "parameters": {"parent": {"pattern": "^projects/[^/]+/locations/[^/]+$", "location": "path", "required": true, "type": "string"}, "pageSize": {"description": "The maximum number of items to return per page.", "location": "query", "type": "integer", "format": "int32"}, "pageToken": {"description": "The next_page_token value returned from a previous List request, if any.", "location": "query", "type": "string"}}, "parameterOrder": ["parent"], "response": {"$ref": "ListReservationGroupsResponse"}, "scopes": ["https://www.googleapis.com/auth/bigquery", "https://www.googleapis.com/auth/cloud-platform"], "description": "Lists all the reservation groups for the project in the specified location."}}}}}}}}, "endpoints": [{"endpointUrl": "https://bigqueryreservation.europe-west3.rep.googleapis.com/", "location": "europe-west3", "description": "Regional Endpoint"}, {"endpointUrl": "https://bigqueryreservation.europe-west8.rep.googleapis.com/", "location": "europe-west8", "description": "Regional Endpoint"}, {"endpointUrl": "https://bigqueryreservation.europe-west9.rep.googleapis.com/", "location": "europe-west9", "description": "Regional Endpoint"}, {"endpointUrl": "https://bigqueryreservation.me-central2.rep.googleapis.com/", "location": "me-central2", "description": "Regional Endpoint"}, {"endpointUrl": "https://bigqueryreservation.us-central1.rep.googleapis.com/", "location": "us-central1", "description": "Regional Endpoint"}, {"endpointUrl": "https://bigqueryreservation.us-central2.rep.googleapis.com/", "location": "us-central2", "description": "Regional Endpoint"}, {"endpointUrl": "https://bigqueryreservation.us-east1.rep.googleapis.com/", "location": "us-east1", "description": "Regional Endpoint"}, {"endpointUrl": "https://bigqueryreservation.us-east4.rep.googleapis.com/", "location": "us-east4", "description": "Regional Endpoint"}, {"endpointUrl": "https://bigqueryreservation.us-east5.rep.googleapis.com/", "location": "us-east5", "description": "Regional Endpoint"}, {"endpointUrl": "https://bigqueryreservation.us-east7.rep.googleapis.com/", "location": "us-east7", "description": "Regional Endpoint"}, {"endpointUrl": "https://bigqueryreservation.us-south1.rep.googleapis.com/", "location": "us-south1", "description": "Regional Endpoint"}, {"endpointUrl": "https://bigqueryreservation.us-west1.rep.googleapis.com/", "location": "us-west1", "description": "Regional Endpoint"}, {"endpointUrl": "https://bigqueryreservation.us-west2.rep.googleapis.com/", "location": "us-west2", "description": "Regional Endpoint"}, {"endpointUrl": "https://bigqueryreservation.us-west3.rep.googleapis.com/", "location": "us-west3", "description": "Regional Endpoint"}, {"endpointUrl": "https://bigqueryreservation.us-west4.rep.googleapis.com/", "location": "us-west4", "description": "Regional Endpoint"}, {"endpointUrl": "https://bigqueryreservation.us-west8.rep.googleapis.com/", "location": "us-west8", "description": "Regional Endpoint"}], "basePath": ""}