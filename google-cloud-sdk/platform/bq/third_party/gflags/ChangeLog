Wed Jan 18 13:57:39 2012  Google Inc. <<EMAIL>>

	* python-gflags: version 2.0
	* No changes from version 1.8.

Wed Jan 18 11:54:03 2012  Google Inc. <<EMAIL>>

	* python-gflags: version 1.8
	* Don't raise DuplicateFlag when re-importing a module (mmcdonald)
	* Changed the 'official' python-gflags email in setup.py/etc
	* Changed copyright text to reflect Google's relinquished ownership

Tue Dec 20 17:10:41 2011  Google Inc. <<EMAIL>>

	* python-gflags: version 1.7
	* Prepare gflags for python 3.x, keeping 2.4 compatibility (twouters)
	* If output is a tty, use terminal's width to wrap help-text (w<PERSON>mann)
	* PORTING: Fix ImportError for non-Unix platforms (kdeus)
	* PORTING: Run correctly when termios isn't available (shines)
	* Add unicode support to flags (csilvers)

Fri Jul 29 12:24:08 2011  Google Inc. <<EMAIL>>

	* python-gflags: version 1.6
	* Document FlagValues.UseGnuGetOpt (garymm)
	* replace fchmod with chmod to work on python 2.4 (mshields)
	* Fix bug in flag decl reporting for dup flags (craigcitro)
	* Add multi_float, and tests for multi_float/int (simonf)
	* Make flagfiles expand in place, to follow docs (dmlynch)
	* Raise exception if --flagfile can't be read (tlim)

Wed Jan 26 13:50:46 2011  Google Inc. <<EMAIL>>

	* python-gflags: version 1.5.1
	* Fix manifest and setup.py to include new files

Mon Jan 24 16:58:10 2011  Google Inc. <<EMAIL>>

	* python-gflags: version 1.5
	* Add support for flag validators (olexiy)
	* Better reporting on UnrecognizedFlagError (sorenj)
	* Cache ArgumentParser, to save space (tmarek)

Wed Oct 13 17:40:12 2010  Google Inc. <<EMAIL>>

	* python-gflags: version 1.4
	* Unregister per-command flags after running the command (dnr)
	* Allow key-flags to work with special flags (salcianu)
	* Allow printing flags of a specific module (mikecurtis)
	* BUGFIX: Fix an error message for float flags (olexiy)
	* BUGFIX: Can now import while defining flags (salcianu)
	* BUGFIX: Fix flagfile parsing in python (chronos)
	* DOC: Better explain the format of --helpxml output (salcianu)
	* DOC: Better error message on parse failure (tstromberg)
	* Better test coverage under python 2.2 (mshields)
	* Added a Makefile for building the packages.

Mon Jan  4 18:46:29 2010  Tim 'mithro' Ansell  <<EMAIL>>

	* python-gflags: version 1.3
	* Fork from the C++ package (google-gflags 1.3)
	* Add debian packaging
