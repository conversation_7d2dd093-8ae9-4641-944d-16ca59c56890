{"compilerOptions": {"target": "ES2022", "module": "nodenext", "moduleResolution": "nodenext", "outDir": "./dist", "rootDir": "./", "strict": true, "esModuleInterop": true, "forceConsistentCasingInFileNames": true, "noImplicitReturns": true, "noFallthroughCasesInSwitch": true, "skipLibCheck": true, "types": ["node", "jest"], "typeRoots": ["./@types", "./node_modules/@types", "./workspace/resources/*/node_modules/@types"]}, "include": ["workspace/**/*"], "exclude": ["node_modules", "dist", "**/*.spec.ts", "src/red-teaming/**/*"]}