#!/bin/bash
# Script to monitor mTLS connections and verify security

set -e

# Configuration
ENVIRONMENT=${1:-staging}
COMPOSE_FILE=${2:-docker/${ENVIRONMENT}-mtls.yml}
MONITOR_DURATION=${3:-60}  # seconds

echo "🔍 Monitoring mTLS Connections for $ENVIRONMENT environment"
echo "📁 Compose file: $COMPOSE_FILE"
echo "⏱️  Duration: ${MONITOR_DURATION}s"
echo ""

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to log with timestamp
log() {
  echo -e "[$(date '+%Y-%m-%d %H:%M:%S')] $1"
}

# Function to check if services are running
check_services() {
  log "${BLUE}🔍 Checking service status...${NC}"
  
  local services=("public-api" "audio-splitter-ffmpeg" "audio-speak-dia-pyannote" "open-parse")
  local all_running=true
  
  for service in "${services[@]}"; do
    if docker compose -f "$COMPOSE_FILE" ps "$service" | grep -q "Up"; then
      log "${GREEN}✅ $service is running${NC}"
    else
      log "${RED}❌ $service is not running${NC}"
      all_running=false
    fi
  done
  
  if [ "$all_running" = false ]; then
    log "${RED}❌ Not all services are running. Please start services first.${NC}"
    exit 1
  fi
  
  log "${GREEN}✅ All services are running${NC}"
  echo ""
}

# Function to monitor mTLS handshakes
monitor_mtls_handshakes() {
  log "${BLUE}🔐 Monitoring mTLS handshakes...${NC}"
  
  # Create temporary files for log monitoring
  local temp_dir=$(mktemp -d)
  local handshake_log="$temp_dir/mtls_handshakes.log"
  local error_log="$temp_dir/mtls_errors.log"
  
  # Monitor logs for mTLS activity
  timeout "$MONITOR_DURATION" docker compose -f "$COMPOSE_FILE" logs -f --tail=100 | \
    grep -E "(mTLS|TLS|certificate|handshake|SSL)" | \
    tee "$handshake_log" | \
    while read -r line; do
      if echo "$line" | grep -qi "handshake.*success\|mTLS.*success\|certificate.*valid"; then
        log "${GREEN}🔐 mTLS Success: $line${NC}"
      elif echo "$line" | grep -qi "handshake.*fail\|mTLS.*fail\|certificate.*invalid\|SSL.*error"; then
        log "${RED}❌ mTLS Error: $line${NC}"
        echo "$line" >> "$error_log"
      else
        log "${YELLOW}🔍 mTLS Activity: $line${NC}"
      fi
    done
  
  # Analyze results
  local success_count=$(grep -c -i "success\|valid" "$handshake_log" 2>/dev/null || echo "0")
  local error_count=$(wc -l < "$error_log" 2>/dev/null || echo "0")
  
  echo ""
  log "${BLUE}📊 mTLS Monitoring Results:${NC}"
  log "${GREEN}✅ Successful handshakes: $success_count${NC}"
  log "${RED}❌ Failed handshakes: $error_count${NC}"
  
  if [ "$error_count" -gt 0 ]; then
    log "${RED}⚠️  Errors detected in mTLS handshakes${NC}"
    log "${YELLOW}📄 Error details:${NC}"
    cat "$error_log"
  fi
  
  # Cleanup
  rm -rf "$temp_dir"
}

# Function to test service connectivity
test_service_connectivity() {
  log "${BLUE}🧪 Testing service connectivity...${NC}"
  
  # Get container names
  local api_container=$(docker compose -f "$COMPOSE_FILE" ps -q public-api)
  
  if [ -z "$api_container" ]; then
    log "${RED}❌ Could not find public-api container${NC}"
    return 1
  fi
  
  # Test internal service calls
  local services=("audio-splitter-ffmpeg:5000" "audio-speak-dia-pyannote:5000" "open-parse:5000")
  
  for service in "${services[@]}"; do
    log "${BLUE}🔗 Testing connection to $service...${NC}"
    
    # Test with curl from inside the container
    if docker exec "$api_container" curl -k -s --max-time 10 "https://$service/health" > /dev/null 2>&1; then
      log "${GREEN}✅ Connection to $service successful${NC}"
    else
      log "${RED}❌ Connection to $service failed${NC}"
    fi
  done
  
  echo ""
}

# Function to verify certificate configuration
verify_certificates() {
  log "${BLUE}📜 Verifying certificate configuration...${NC}"
  
  local services=("public-api" "audio-splitter-ffmpeg" "audio-speak-dia-pyannote" "open-parse")
  
  for service in "${services[@]}"; do
    local container=$(docker compose -f "$COMPOSE_FILE" ps -q "$service")
    
    if [ -n "$container" ]; then
      log "${BLUE}🔍 Checking certificates in $service...${NC}"
      
      # Check if certificates are mounted
      if docker exec "$container" test -f "/etc/ssl/ca/ca.crt" 2>/dev/null; then
        log "${GREEN}✅ CA certificate found in $service${NC}"
      else
        log "${RED}❌ CA certificate missing in $service${NC}"
      fi
      
      # Check service-specific certificates
      if [ "$service" = "public-api" ]; then
        if docker exec "$container" test -f "/etc/ssl/certs/client.crt" 2>/dev/null; then
          log "${GREEN}✅ Client certificate found in $service${NC}"
        else
          log "${RED}❌ Client certificate missing in $service${NC}"
        fi
      else
        if docker exec "$container" test -f "/etc/ssl/certs/server.crt" 2>/dev/null; then
          log "${GREEN}✅ Server certificate found in $service${NC}"
        else
          log "${RED}❌ Server certificate missing in $service${NC}"
        fi
      fi
    fi
  done
  
  echo ""
}

# Function to check environment variables
check_environment() {
  log "${BLUE}⚙️  Checking mTLS environment variables...${NC}"
  
  local services=("public-api" "audio-splitter-ffmpeg" "audio-speak-dia-pyannote" "open-parse")
  
  for service in "${services[@]}"; do
    local container=$(docker compose -f "$COMPOSE_FILE" ps -q "$service")
    
    if [ -n "$container" ]; then
      log "${BLUE}🔍 Checking environment in $service...${NC}"
      
      # Check ENABLE_MTLS
      if docker exec "$container" printenv ENABLE_MTLS | grep -q "true"; then
        log "${GREEN}✅ ENABLE_MTLS=true in $service${NC}"
      else
        log "${RED}❌ ENABLE_MTLS not set to true in $service${NC}"
      fi
      
      # Check MTLS_CERT_DIR
      if docker exec "$container" printenv MTLS_CERT_DIR > /dev/null 2>&1; then
        local cert_dir=$(docker exec "$container" printenv MTLS_CERT_DIR)
        log "${GREEN}✅ MTLS_CERT_DIR=$cert_dir in $service${NC}"
      else
        log "${YELLOW}⚠️  MTLS_CERT_DIR not set in $service${NC}"
      fi
    fi
  done
  
  echo ""
}

# Function to generate security report
generate_security_report() {
  log "${BLUE}📋 Generating security report...${NC}"
  
  local report_file="mtls-security-report-$ENVIRONMENT-$(date +%Y%m%d-%H%M%S).txt"
  
  {
    echo "mTLS Security Report for $ENVIRONMENT Environment"
    echo "Generated: $(date)"
    echo "=========================================="
    echo ""
    
    echo "Service Status:"
    docker compose -f "$COMPOSE_FILE" ps
    echo ""
    
    echo "Certificate Verification:"
    # Add certificate verification results here
    echo ""
    
    echo "Environment Variables:"
    # Add environment variable checks here
    echo ""
    
    echo "Network Configuration:"
    docker network ls | grep "$ENVIRONMENT"
    echo ""
    
  } > "$report_file"
  
  log "${GREEN}📄 Security report saved to: $report_file${NC}"
}

# Main execution
main() {
  log "${GREEN}🚀 Starting mTLS monitoring for $ENVIRONMENT environment${NC}"
  echo ""
  
  # Check if compose file exists
  if [ ! -f "$COMPOSE_FILE" ]; then
    log "${RED}❌ Compose file not found: $COMPOSE_FILE${NC}"
    exit 1
  fi
  
  # Run monitoring steps
  check_services
  verify_certificates
  check_environment
  test_service_connectivity
  monitor_mtls_handshakes
  generate_security_report
  
  log "${GREEN}🎉 mTLS monitoring complete!${NC}"
}

# Run main function
main "$@"
