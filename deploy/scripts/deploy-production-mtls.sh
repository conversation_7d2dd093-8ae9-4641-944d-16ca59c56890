#!/bin/bash
# Complete production deployment script for mTLS

set -e

# Configuration
ENVIRONMENT=${1:-staging}
DRY_RUN=${2:-false}
SKIP_BUILD=${3:-false}

echo "🚀 Deploying mTLS to $ENVIRONMENT environment"
echo "🧪 Dry run: $DRY_RUN"
echo "⏭️  Skip build: $SKIP_BUILD"
echo ""

# Colors
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# Function to log with timestamp
log() {
  echo -e "[$(date '+%Y-%m-%d %H:%M:%S')] $1"
}

# Function to check prerequisites
check_prerequisites() {
  log "${BLUE}🔍 Checking prerequisites...${NC}"
  
  # Check if certificates exist
  if [ ! -d "./private-keys/$ENVIRONMENT/certs/mtls" ]; then
    log "${RED}❌ Certificates not found for $ENVIRONMENT${NC}"
    log "${YELLOW}💡 Run: ./deploy/scripts/generate-mtls-certs.sh $ENVIRONMENT${NC}"
    exit 1
  fi
  
  # Check if environment files exist
  if [ ! -f "./private-keys/$ENVIRONMENT/mtls.env" ]; then
    log "${RED}❌ Environment configuration not found for $ENVIRONMENT${NC}"
    exit 1
  fi
  
  # Check if Docker Compose file exists
  if [ ! -f "./docker/$ENVIRONMENT-mtls.yml" ]; then
    log "${RED}❌ Docker Compose file not found: docker/$ENVIRONMENT-mtls.yml${NC}"
    exit 1
  fi
  
  log "${GREEN}✅ Prerequisites check passed${NC}"
  echo ""
}

# Function to deploy certificates
deploy_certificates() {
  log "${BLUE}📜 Deploying certificates...${NC}"
  
  if [ "$DRY_RUN" = "false" ]; then
    ./deploy/scripts/deploy-mtls-certificates.sh "$ENVIRONMENT" docker false
  else
    log "${YELLOW}🧪 [DRY RUN] Would deploy certificates for $ENVIRONMENT${NC}"
  fi
  
  echo ""
}

# Function to build services
build_services() {
  if [ "$SKIP_BUILD" = "true" ]; then
    log "${YELLOW}⏭️  Skipping build step${NC}"
    return
  fi
  
  log "${BLUE}🔨 Building services...${NC}"
  
  if [ "$DRY_RUN" = "false" ]; then
    docker compose -f "docker/$ENVIRONMENT-mtls.yml" build --no-cache
  else
    log "${YELLOW}🧪 [DRY RUN] Would build services${NC}"
  fi
  
  echo ""
}

# Function to start services
start_services() {
  log "${BLUE}🚀 Starting services with mTLS...${NC}"
  
  if [ "$DRY_RUN" = "false" ]; then
    # Stop any existing services
    docker compose -f "docker/$ENVIRONMENT-mtls.yml" down --remove-orphans
    
    # Start services
    docker compose -f "docker/$ENVIRONMENT-mtls.yml" up -d
    
    # Wait for services to be ready
    log "${BLUE}⏳ Waiting for services to be ready...${NC}"
    sleep 30
    
    # Check service health
    docker compose -f "docker/$ENVIRONMENT-mtls.yml" ps
  else
    log "${YELLOW}🧪 [DRY RUN] Would start services${NC}"
  fi
  
  echo ""
}

# Function to verify deployment
verify_deployment() {
  log "${BLUE}🔍 Verifying deployment...${NC}"
  
  if [ "$DRY_RUN" = "false" ]; then
    # Run monitoring script for a short duration
    ./deploy/scripts/monitor-mtls.sh "$ENVIRONMENT" "docker/$ENVIRONMENT-mtls.yml" 30
  else
    log "${YELLOW}🧪 [DRY RUN] Would verify deployment${NC}"
  fi
  
  echo ""
}

# Function to run health checks
run_health_checks() {
  log "${BLUE}🏥 Running health checks...${NC}"
  
  if [ "$DRY_RUN" = "false" ]; then
    local services=("public-api" "audio-splitter-ffmpeg" "audio-speak-dia-pyannote" "open-parse")
    local all_healthy=true
    
    for service in "${services[@]}"; do
      log "${BLUE}🔍 Checking health of $service...${NC}"
      
      local container=$(docker compose -f "docker/$ENVIRONMENT-mtls.yml" ps -q "$service")
      
      if [ -n "$container" ]; then
        # Check if container is running
        if docker ps --filter "id=$container" --filter "status=running" | grep -q "$container"; then
          log "${GREEN}✅ $service is running${NC}"
          
          # Check health endpoint if available
          if [ "$service" = "public-api" ]; then
            if docker exec "$container" curl -f -s http://localhost:8080/health > /dev/null 2>&1; then
              log "${GREEN}✅ $service health check passed${NC}"
            else
              log "${RED}❌ $service health check failed${NC}"
              all_healthy=false
            fi
          fi
        else
          log "${RED}❌ $service is not running${NC}"
          all_healthy=false
        fi
      else
        log "${RED}❌ $service container not found${NC}"
        all_healthy=false
      fi
    done
    
    if [ "$all_healthy" = true ]; then
      log "${GREEN}🎉 All services are healthy!${NC}"
    else
      log "${RED}⚠️  Some services are not healthy${NC}"
    fi
  else
    log "${YELLOW}🧪 [DRY RUN] Would run health checks${NC}"
  fi
  
  echo ""
}

# Function to display deployment summary
display_summary() {
  log "${BLUE}📋 Deployment Summary${NC}"
  echo ""
  
  log "${GREEN}✅ Environment: $ENVIRONMENT${NC}"
  log "${GREEN}✅ mTLS Enabled: true${NC}"
  log "${GREEN}✅ Certificates: Deployed${NC}"
  log "${GREEN}✅ Services: Started${NC}"
  
  if [ "$DRY_RUN" = "false" ]; then
    log "${GREEN}✅ Status: Deployed${NC}"
    echo ""
    log "${BLUE}🔗 Service URLs:${NC}"
    log "${BLUE}   Public API: https://localhost:$([ "$ENVIRONMENT" = "production" ] && echo "443" || echo "9443")${NC}"
    echo ""
    log "${BLUE}📊 Monitoring Commands:${NC}"
    log "${BLUE}   Monitor logs: ./deploy/scripts/monitor-mtls.sh $ENVIRONMENT${NC}"
    log "${BLUE}   View services: docker compose -f docker/$ENVIRONMENT-mtls.yml ps${NC}"
    log "${BLUE}   View logs: docker compose -f docker/$ENVIRONMENT-mtls.yml logs -f${NC}"
    echo ""
    log "${BLUE}🛑 Stop Services:${NC}"
    log "${BLUE}   docker compose -f docker/$ENVIRONMENT-mtls.yml down${NC}"
  else
    log "${YELLOW}🧪 Status: Dry run completed${NC}"
  fi
  
  echo ""
}

# Main execution
main() {
  log "${GREEN}🚀 Starting mTLS deployment for $ENVIRONMENT${NC}"
  echo ""
  
  # Validate environment
  if [[ ! "$ENVIRONMENT" =~ ^(staging|production)$ ]]; then
    log "${RED}❌ Invalid environment: $ENVIRONMENT${NC}"
    log "${YELLOW}💡 Valid environments: staging, production${NC}"
    exit 1
  fi
  
  # Run deployment steps
  check_prerequisites
  deploy_certificates
  build_services
  start_services
  verify_deployment
  run_health_checks
  display_summary
  
  log "${GREEN}🎉 mTLS deployment complete!${NC}"
}

# Show usage if no arguments
if [ $# -eq 0 ]; then
  echo "Usage: $0 <environment> [dry_run] [skip_build]"
  echo ""
  echo "Arguments:"
  echo "  environment  - staging or production"
  echo "  dry_run      - true/false (default: false)"
  echo "  skip_build   - true/false (default: false)"
  echo ""
  echo "Examples:"
  echo "  $0 staging"
  echo "  $0 staging true"
  echo "  $0 production false true"
  exit 1
fi

# Run main function
main "$@"
