#!/bin/bash
# Script to deploy mTLS certificates to production environments

set -e

# Configuration
ENVIRONMENT=${1:-staging}
DEPLOYMENT_TYPE=${2:-docker}  # docker, k8s, or gcp
DRY_RUN=${3:-false}

echo "🔒 Deploying mTLS Certificates for $ENVIRONMENT environment"
echo "📦 Deployment type: $DEPLOYMENT_TYPE"
echo "🧪 Dry run: $DRY_RUN"
echo ""

# Paths
CERT_SOURCE_DIR="./private-keys/$ENVIRONMENT/certs/mtls"
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"

# Validate environment
if [[ ! "$ENVIRONMENT" =~ ^(staging|production)$ ]]; then
  echo "❌ Invalid environment: $ENVIRONMENT"
  echo "   Valid environments: staging, production"
  exit 1
fi

# Check if certificates exist
if [ ! -d "$CERT_SOURCE_DIR" ]; then
  echo "❌ Certificate directory not found: $CERT_SOURCE_DIR"
  echo "   Run: ./deploy/scripts/generate-mtls-certs.sh $ENVIRONMENT"
  exit 1
fi

# Function to validate certificate
validate_certificate() {
  local cert_file=$1
  local cert_name=$2
  
  if [ ! -f "$cert_file" ]; then
    echo "❌ Certificate not found: $cert_file"
    return 1
  fi
  
  # Check certificate format
  if ! openssl x509 -in "$cert_file" -text -noout > /dev/null 2>&1; then
    echo "❌ Invalid certificate format: $cert_name"
    return 1
  fi
  
  # Check expiration
  local expiry_date=$(openssl x509 -in "$cert_file" -enddate -noout | cut -d= -f2)
  local expiry_epoch=$(date -d "$expiry_date" +%s)
  local current_epoch=$(date +%s)
  local days_until_expiry=$(( (expiry_epoch - current_epoch) / 86400 ))
  
  if [ $days_until_expiry -lt 30 ]; then
    echo "⚠️  Certificate expires in $days_until_expiry days: $cert_name"
  else
    echo "✅ Certificate valid for $days_until_expiry days: $cert_name"
  fi
  
  return 0
}

# Function to validate private key
validate_private_key() {
  local key_file=$1
  local key_name=$2
  
  if [ ! -f "$key_file" ]; then
    echo "❌ Private key not found: $key_file"
    return 1
  fi
  
  # Check key format
  if ! openssl rsa -in "$key_file" -check -noout > /dev/null 2>&1; then
    echo "❌ Invalid private key format: $key_name"
    return 1
  fi
  
  echo "✅ Private key valid: $key_name"
  return 0
}

# Function to deploy to Docker environment
deploy_docker() {
  echo "🐳 Deploying certificates for Docker environment..."
  
  # Create deployment directory structure
  local deploy_dir="./deploy/certificates/$ENVIRONMENT"
  
  if [ "$DRY_RUN" = "false" ]; then
    mkdir -p "$deploy_dir/ca"
    mkdir -p "$deploy_dir/services"
    
    # Copy CA certificate
    cp "$CERT_SOURCE_DIR/ca/ca.crt" "$deploy_dir/ca/"
    echo "✅ Copied CA certificate to $deploy_dir/ca/"
    
    # Copy service certificates
    for service in public-api ffmpeg open-parse pyannote; do
      mkdir -p "$deploy_dir/services/$service"
      cp "$CERT_SOURCE_DIR/services/$service/"* "$deploy_dir/services/$service/"
      echo "✅ Copied $service certificates to $deploy_dir/services/$service/"
    done
    
    # Create Docker Compose override for mTLS
    cat > "./docker/mtls-$ENVIRONMENT.yml" << EOF
# mTLS configuration for $ENVIRONMENT environment
# Generated by deploy-mtls-certificates.sh

version: '3.8'

services:
  public-api:
    environment:
      - ENABLE_MTLS=true
      - DEBUG_MTLS=true
      - MTLS_CERT_DIR=/etc/ssl
      - MTLS_VERIFY_SERVER=true
    volumes:
      - ../deploy/certificates/$ENVIRONMENT/ca/ca.crt:/etc/ssl/ca/ca.crt:ro
      - ../deploy/certificates/$ENVIRONMENT/services/public-api/client.crt:/etc/ssl/certs/client.crt:ro
      - ../deploy/certificates/$ENVIRONMENT/services/public-api/client.key:/etc/ssl/private/client.key:ro

  audio-splitter-ffmpeg:
    environment:
      - ENABLE_MTLS=true
      - MTLS_CERT_DIR=/etc/ssl
    volumes:
      - ../deploy/certificates/$ENVIRONMENT/ca/ca.crt:/etc/ssl/ca/ca.crt:ro
      - ../deploy/certificates/$ENVIRONMENT/services/ffmpeg/server.crt:/etc/ssl/certs/server.crt:ro
      - ../deploy/certificates/$ENVIRONMENT/services/ffmpeg/server.key:/etc/ssl/private/server.key:ro

  audio-speak-dia-pyannote:
    environment:
      - ENABLE_MTLS=true
      - MTLS_CERT_DIR=/etc/ssl
    volumes:
      - ../deploy/certificates/$ENVIRONMENT/ca/ca.crt:/etc/ssl/ca/ca.crt:ro
      - ../deploy/certificates/$ENVIRONMENT/services/pyannote/server.crt:/etc/ssl/certs/server.crt:ro
      - ../deploy/certificates/$ENVIRONMENT/services/pyannote/server.key:/etc/ssl/private/server.key:ro

  open-parse:
    environment:
      - ENABLE_MTLS=true
      - MTLS_CERT_DIR=/etc/ssl
    volumes:
      - ../deploy/certificates/$ENVIRONMENT/ca/ca.crt:/etc/ssl/ca/ca.crt:ro
      - ../deploy/certificates/$ENVIRONMENT/services/open-parse/server.crt:/etc/ssl/certs/server.crt:ro
      - ../deploy/certificates/$ENVIRONMENT/services/open-parse/server.key:/etc/ssl/private/server.key:ro
EOF
    
    echo "✅ Created Docker Compose override: docker/mtls-$ENVIRONMENT.yml"
  else
    echo "🧪 [DRY RUN] Would copy certificates to $deploy_dir"
    echo "🧪 [DRY RUN] Would create docker/mtls-$ENVIRONMENT.yml"
  fi
}

# Function to deploy to Google Cloud Platform
deploy_gcp() {
  echo "☁️  Deploying certificates for Google Cloud Platform..."
  
  if [ "$DRY_RUN" = "false" ]; then
    # Create secrets in Google Secret Manager
    echo "Creating secrets in Google Secret Manager..."
    
    # CA certificate
    gcloud secrets create "mtls-ca-cert-$ENVIRONMENT" \
      --data-file="$CERT_SOURCE_DIR/ca/ca.crt" \
      --replication-policy="automatic" || echo "Secret may already exist"
    
    # Service certificates
    for service in public-api ffmpeg open-parse pyannote; do
      # Server certificate
      gcloud secrets create "mtls-$service-server-cert-$ENVIRONMENT" \
        --data-file="$CERT_SOURCE_DIR/services/$service/server.crt" \
        --replication-policy="automatic" || echo "Secret may already exist"
      
      # Server key
      gcloud secrets create "mtls-$service-server-key-$ENVIRONMENT" \
        --data-file="$CERT_SOURCE_DIR/services/$service/server.key" \
        --replication-policy="automatic" || echo "Secret may already exist"
    done
    
    # Client certificates for public-api
    gcloud secrets create "mtls-public-api-client-cert-$ENVIRONMENT" \
      --data-file="$CERT_SOURCE_DIR/services/public-api/client.crt" \
      --replication-policy="automatic" || echo "Secret may already exist"
    
    gcloud secrets create "mtls-public-api-client-key-$ENVIRONMENT" \
      --data-file="$CERT_SOURCE_DIR/services/public-api/client.key" \
      --replication-policy="automatic" || echo "Secret may already exist"
    
    echo "✅ Certificates deployed to Google Secret Manager"
  else
    echo "🧪 [DRY RUN] Would create secrets in Google Secret Manager"
  fi
}

# Main deployment logic
echo "🔍 Validating certificates..."

# Validate CA certificate
validate_certificate "$CERT_SOURCE_DIR/ca/ca.crt" "CA Certificate"

# Validate service certificates
services=("public-api" "ffmpeg" "open-parse" "pyannote")
for service in "${services[@]}"; do
  validate_certificate "$CERT_SOURCE_DIR/services/$service/server.crt" "$service Server Certificate"
  validate_private_key "$CERT_SOURCE_DIR/services/$service/server.key" "$service Server Key"
done

# Validate client certificates for public-api
validate_certificate "$CERT_SOURCE_DIR/services/public-api/client.crt" "Public API Client Certificate"
validate_private_key "$CERT_SOURCE_DIR/services/public-api/client.key" "Public API Client Key"

echo ""
echo "✅ All certificates validated successfully!"
echo ""

# Deploy based on type
case $DEPLOYMENT_TYPE in
  docker)
    deploy_docker
    ;;
  gcp)
    deploy_gcp
    ;;
  *)
    echo "❌ Unsupported deployment type: $DEPLOYMENT_TYPE"
    echo "   Supported types: docker, gcp"
    exit 1
    ;;
esac

echo ""
echo "🎉 Certificate deployment complete!"
echo ""
echo "📋 Next steps:"
echo "   1. Update service configurations with ENABLE_MTLS=true"
echo "   2. Deploy services using the mTLS-enabled configuration"
echo "   3. Monitor logs for successful mTLS handshakes"
echo "   4. Verify all service-to-service communication is encrypted"
