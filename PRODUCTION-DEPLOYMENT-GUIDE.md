# mTLS Production Deployment Guide

## 🎯 **Overview**

This guide walks you through deploying the mTLS implementation to staging and production environments. All scripts and configurations have been prepared and tested.

## 📋 **Prerequisites**

- Docker and Docker Compose installed
- OpenSSL for certificate validation
- Access to staging/production environments
- Network connectivity between services

## 🚀 **Quick Start**

### **Option 1: Automated Deployment**

```bash
# Deploy to staging
./deploy/scripts/deploy-production-mtls.sh staging

# Deploy to production
./deploy/scripts/deploy-production-mtls.sh production
```

### **Option 2: Step-by-Step Deployment**

Follow the detailed steps below for manual control.

## 📝 **Step-by-Step Deployment**

### **Step 1: Deploy Certificates**

#### Generate Certificates (if not already done)

```bash
# For staging
./deploy/scripts/generate-mtls-certs.sh staging

# For production
./deploy/scripts/generate-mtls-certs.sh production
```

#### Deploy Certificates

```bash
# For staging
./deploy/scripts/deploy-mtls-certificates.sh staging docker false

# For production
./deploy/scripts/deploy-mtls-certificates.sh production docker false
```

**Expected Output:**

- ✅ Certificates validated (valid for 729+ days)
- ✅ Certificates copied to deployment directories
- ✅ Docker Compose override files created

### **Step 2: Update Configuration**

Configuration files are automatically created in:

- `private-keys/staging/` - Staging environment config
- `private-keys/production/` - Production environment config

**Key Configuration Files:**

- `mtls.env` - mTLS-specific settings
- `endpoints.shared.env` - Service URLs and general config
- `mongodb.env` - Database configuration
- `redis.env` - Redis configuration

### **Step 3: Start Services**

#### Staging Deployment

```bash
# Build and start staging services
docker compose -f docker/staging-mtls.yml up --build -d

# Check service status
docker compose -f docker/staging-mtls.yml ps
```

#### Production Deployment

```bash
# Build and start production services
docker compose -f docker/production-mtls.yml up --build -d

# Check service status
docker compose -f docker/production-mtls.yml ps
```

### **Step 4: Monitor and Verify**

#### Monitor mTLS Activity

```bash
# Monitor staging for 60 seconds
./deploy/scripts/monitor-mtls.sh staging docker/staging-mtls.yml 60

# Monitor production for 60 seconds
./deploy/scripts/monitor-mtls.sh production docker/production-mtls.yml 60
```

#### Manual Verification

```bash
# Check service logs
docker compose -f docker/staging-mtls.yml logs -f public-api

# Test service connectivity
curl -k https://localhost:9443/health  # Staging
curl -k https://localhost:443/health   # Production
```

## 🔍 **Verification Checklist**

### **Certificate Verification**

- [ ] CA certificate exists and is valid
- [ ] Service certificates exist for all services
- [ ] Client certificates exist for public-api
- [ ] All certificates are properly mounted in containers

### **Service Verification**

- [ ] All services are running and healthy
- [ ] Services can communicate with each other
- [ ] mTLS handshakes are successful
- [ ] No certificate errors in logs

### **Security Verification**

- [ ] `ENABLE_MTLS=true` in all services
- [ ] Certificate verification is enabled
- [ ] Internal traffic uses HTTPS with mTLS
- [ ] External APIs continue using regular HTTPS

## 📊 **Monitoring Commands**

### **Service Status**

```bash
# Check all services
docker compose -f docker/staging-mtls.yml ps

# Check specific service
docker compose -f docker/staging-mtls.yml ps public-api
```

### **Log Monitoring**

```bash
# Follow all logs
docker compose -f docker/staging-mtls.yml logs -f

# Follow specific service logs
docker compose -f docker/staging-mtls.yml logs -f public-api

# Search for mTLS activity
docker compose -f docker/staging-mtls.yml logs | grep -i mtls
```

### **Health Checks**

```bash
# Public API health
curl -f https://localhost:9443/health

# Internal service health (from within public-api container)
docker exec $(docker compose -f docker/staging-mtls.yml ps -q public-api) \
  curl -k https://audio-splitter-ffmpeg:5000/health
```

## 🛠️ **Troubleshooting**

### **Common Issues**

#### Certificate Errors

```bash
# Check certificate validity
openssl x509 -in private-keys/staging/certs/mtls/ca/ca.crt -text -noout

# Verify certificate chain
openssl verify -CAfile private-keys/staging/certs/mtls/ca/ca.crt \
  private-keys/staging/certs/mtls/services/public-api/client.crt
```

#### Service Connection Issues

```bash
# Check network connectivity
docker network ls | grep staging

# Check service DNS resolution
docker exec $(docker compose -f docker/staging-mtls.yml ps -q public-api) \
  nslookup audio-splitter-ffmpeg
```

#### Environment Variable Issues

```bash
# Check mTLS environment variables
docker exec $(docker compose -f docker/staging-mtls.yml ps -q public-api) \
  printenv | grep MTLS
```

### **Debug Mode**

Enable debug logging for troubleshooting:

```bash
# Edit environment file
echo "DEBUG_MTLS=true" >> private-keys/staging/mtls.env
echo "DEBUG=app,mtls" >> private-keys/staging/mtls.env

# Restart services
docker compose -f docker/staging-mtls.yml restart
```

## 🔄 **Rollback Procedure**

If issues occur, rollback to non-mTLS configuration:

```bash
# Stop mTLS services
docker compose -f docker/staging-mtls.yml down

# Start regular services
docker compose -f docker/local.yml up -d
```

## 📈 **Performance Monitoring**

### **Key Metrics to Monitor**

- mTLS handshake success rate
- Service response times
- Certificate expiration dates
- Connection pool utilization

### **Monitoring Commands**

```bash
# Monitor response times
time curl -k https://localhost:9443/health

# Check connection counts
docker exec $(docker compose -f docker/staging-mtls.yml ps -q public-api) \
  netstat -an | grep :5000
```

## 🔐 **Security Best Practices**

### **Certificate Management**

- [ ] Certificates stored securely
- [ ] Private keys have restricted permissions (600)
- [ ] CA private key is protected
- [ ] Certificate rotation plan in place

### **Environment Security**

- [ ] Production passwords are strong and unique
- [ ] Debug logging disabled in production
- [ ] Unnecessary ports are not exposed
- [ ] Network segmentation is implemented

## 📋 **Deployment Checklist**

### **Pre-Deployment**

- [ ] Certificates generated and validated
- [ ] Environment configurations reviewed
- [ ] Backup of current configuration taken
- [ ] Rollback plan prepared

### **During Deployment**

- [ ] Services deployed in correct order
- [ ] Health checks passing
- [ ] mTLS handshakes successful
- [ ] No errors in logs

### **Post-Deployment**

- [ ] End-to-end testing completed
- [ ] Performance metrics baseline established
- [ ] Monitoring alerts configured
- [ ] Documentation updated

## 🎉 **Success Criteria**

Deployment is successful when:

- ✅ All services are running and healthy
- ✅ mTLS handshakes are successful
- ✅ Internal service communication is encrypted
- ✅ External APIs continue working
- ✅ No certificate errors in logs
- ✅ Performance is within acceptable limits

## 📞 **Support**

For issues or questions:

1. Check the troubleshooting section above
2. Review service logs for specific error messages
3. Verify certificate validity and configuration
4. Test with debug logging enabled
